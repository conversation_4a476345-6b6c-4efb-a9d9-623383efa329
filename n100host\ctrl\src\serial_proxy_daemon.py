#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100串口代理守护进程
统一管理/dev/ttyS4串口访问，通过Unix Socket为其他进程提供服务
避免多个进程同时访问串口造成冲突

架构设计:
- 唯一占用/dev/ttyS4串口
- 通过Unix Socket接受客户端连接
- 转发串口数据给所有客户端
- 处理客户端发送请求
- 支持消息路由和过滤
"""

import os
import sys
import json
import time
import socket
import serial
import signal
import logging
import threading
import traceback
from pathlib import Path
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass, asdict
from enum import IntEnum

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from protocol import validate_frame, create_ack_frame, ProtocolFrame
except ImportError as e:
    print(f"错误: 无法导入协议模块: {e}")
    sys.exit(1)


class MessageType(IntEnum):
    """消息类型枚举"""
    LED_MODE = 0x01        # LED模式设置命令
    BREATH_PERIOD = 0x02   # 呼吸灯周期设置命令
    SHUTDOWN = 0x03        # 关机成功消息
    SHUTDOWN_REQ = 0x13    # 关机请求命令（电源板发送给N100）
    ACK = 0x80            # 通用应答命令


@dataclass
class ProxyMessage:
    """代理消息格式"""
    msg_type: str          # 'serial_data', 'send_request', 'register', 'unregister'
    client_id: str         # 客户端ID
    data: bytes = b''      # 消息数据
    command: int = 0       # 命令类型
    timestamp: float = 0.0 # 时间戳
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        data_dict = asdict(self)
        data_dict['data'] = self.data.hex() if self.data else ''
        return json.dumps(data_dict)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'ProxyMessage':
        """从JSON字符串创建"""
        data_dict = json.loads(json_str)
        data_dict['data'] = bytes.fromhex(data_dict['data']) if data_dict['data'] else b''
        return cls(**data_dict)


class SerialProxyDaemon:
    """串口代理守护进程"""
    
    def __init__(self, 
                 serial_port: str = '/dev/ttyS4',
                 socket_path: str = '/tmp/n100_serial_proxy.sock',
                 log_file: str = '/var/log/n100_serial_proxy.log'):
        """
        初始化串口代理守护进程
        
        参数:
            serial_port: 串口设备路径
            socket_path: Unix Socket路径
            log_file: 日志文件路径
        """
        self.serial_port = serial_port
        self.socket_path = socket_path
        self.log_file = log_file
        
        # 串口和Socket
        self.serial = None
        self.server_socket = None
        
        # 客户端管理
        self.clients: Dict[str, socket.socket] = {}
        self.client_filters: Dict[str, List[int]] = {}  # 客户端消息过滤器
        self.clients_lock = threading.Lock()
        
        # 线程管理
        self.running = False
        self.serial_thread = None
        self.server_thread = None
        
        # 接收缓冲区
        self.rx_buffer = bytearray()
        self.rx_lock = threading.Lock()
        
        # 日志配置
        self.setup_logging()
        
        # 信号处理
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
    
    def setup_logging(self):
        """设置日志"""
        # 确保日志目录存在
        log_dir = Path(self.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('SerialProxyDaemon')
    
    def start(self) -> bool:
        """启动代理服务"""
        try:
            self.logger.info("启动N100串口代理守护进程...")
            
            # 打开串口
            if not self._open_serial():
                return False
            
            # 启动Unix Socket服务器
            if not self._start_socket_server():
                return False
            
            # 启动串口读取线程
            self._start_serial_thread()
            
            self.running = True
            self.logger.info("串口代理守护进程启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"启动代理服务失败: {e}")
            return False
    
    def stop(self):
        """停止代理服务"""
        self.logger.info("停止串口代理守护进程...")
        self.running = False
        
        # 关闭所有客户端连接
        with self.clients_lock:
            for client_id, client_socket in self.clients.items():
                try:
                    client_socket.close()
                except:
                    pass
            self.clients.clear()
        
        # 关闭服务器Socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        # 删除Socket文件
        try:
            if os.path.exists(self.socket_path):
                os.unlink(self.socket_path)
        except:
            pass
        
        # 关闭串口
        if self.serial and self.serial.is_open:
            self.serial.close()
        
        self.logger.info("串口代理守护进程已停止")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备停止服务...")
        self.stop()
        sys.exit(0)
    
    def _open_serial(self) -> bool:
        """打开串口"""
        try:
            self.serial = serial.Serial(
                port=self.serial_port,
                baudrate=115200,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1.0
            )
            
            self.logger.info(f"串口 {self.serial_port} 打开成功")
            return True
            
        except Exception as e:
            self.logger.error(f"打开串口失败: {e}")
            return False
    
    def _start_socket_server(self) -> bool:
        """启动Unix Socket服务器"""
        try:
            # 删除已存在的Socket文件
            if os.path.exists(self.socket_path):
                os.unlink(self.socket_path)
            
            # 创建Unix Socket
            self.server_socket = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
            self.server_socket.bind(self.socket_path)
            self.server_socket.listen(10)
            
            # 设置Socket权限
            os.chmod(self.socket_path, 0o666)
            
            # 启动服务器线程
            self.server_thread = threading.Thread(target=self._socket_server_loop, daemon=True)
            self.server_thread.start()
            
            self.logger.info(f"Unix Socket服务器启动: {self.socket_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动Socket服务器失败: {e}")
            return False
    
    def _start_serial_thread(self):
        """启动串口读取线程"""
        self.serial_thread = threading.Thread(target=self._serial_read_loop, daemon=True)
        self.serial_thread.start()
        self.logger.info("串口读取线程已启动")
    
    def _socket_server_loop(self):
        """Socket服务器循环"""
        while self.running:
            try:
                client_socket, _ = self.server_socket.accept()
                
                # 启动客户端处理线程
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket,),
                    daemon=True
                )
                client_thread.start()
                
            except Exception as e:
                if self.running:
                    self.logger.error(f"Socket服务器异常: {e}")
                break
    
    def _handle_client(self, client_socket: socket.socket):
        """处理客户端连接"""
        client_id = None
        
        try:
            while self.running:
                # 接收客户端消息
                data = client_socket.recv(4096)
                if not data:
                    break
                
                try:
                    # 解析消息
                    message = ProxyMessage.from_json(data.decode('utf-8'))
                    
                    if message.msg_type == 'register':
                        # 注册客户端
                        client_id = message.client_id
                        with self.clients_lock:
                            self.clients[client_id] = client_socket
                            self.client_filters[client_id] = []
                        
                        self.logger.info(f"客户端注册: {client_id}")
                        
                        # 发送注册确认
                        response = ProxyMessage('register_ack', client_id)
                        self._send_to_client(client_socket, response)
                    
                    elif message.msg_type == 'send_request':
                        # 处理发送请求
                        self._handle_send_request(message)
                    
                    elif message.msg_type == 'set_filter':
                        # 设置消息过滤器
                        if client_id:
                            with self.clients_lock:
                                self.client_filters[client_id] = message.data
                            self.logger.info(f"客户端 {client_id} 设置过滤器: {message.data}")
                
                except json.JSONDecodeError:
                    self.logger.warning("收到无效的JSON消息")
                except Exception as e:
                    self.logger.error(f"处理客户端消息异常: {e}")
        
        except Exception as e:
            self.logger.error(f"客户端处理异常: {e}")
        
        finally:
            # 清理客户端
            if client_id:
                with self.clients_lock:
                    self.clients.pop(client_id, None)
                    self.client_filters.pop(client_id, None)
                self.logger.info(f"客户端断开: {client_id}")
            
            try:
                client_socket.close()
            except:
                pass
    
    def _handle_send_request(self, message: ProxyMessage):
        """处理发送请求"""
        try:
            if self.serial and self.serial.is_open:
                self.serial.write(message.data)
                self.serial.flush()
                self.logger.debug(f"发送数据: {message.data.hex(' ').upper()}")
            else:
                self.logger.error("串口未打开，无法发送数据")
        
        except Exception as e:
            self.logger.error(f"发送数据失败: {e}")
    
    def _serial_read_loop(self):
        """串口读取循环"""
        while self.running:
            try:
                if self.serial and self.serial.in_waiting > 0:
                    data = self.serial.read(self.serial.in_waiting)
                    if data:
                        self.logger.debug(f"接收数据: {data.hex(' ').upper()}")
                        
                        with self.rx_lock:
                            self.rx_buffer.extend(data)
                            self._parse_and_forward_messages()
                
                time.sleep(0.01)
                
            except Exception as e:
                self.logger.error(f"串口读取异常: {e}")
                time.sleep(0.1)
    
    def _parse_and_forward_messages(self):
        """解析并转发消息"""
        while len(self.rx_buffer) >= 5:  # 最小帧长度
            # 查找帧头
            header_pos = -1
            for i in range(len(self.rx_buffer)):
                if self.rx_buffer[i] == 0xAA:
                    header_pos = i
                    break
            
            if header_pos == -1:
                # 没有找到帧头，清空缓冲区
                self.rx_buffer.clear()
                break
            
            if header_pos > 0:
                # 丢弃帧头前的数据
                self.rx_buffer = self.rx_buffer[header_pos:]
            
            # 检查是否有完整的帧
            if len(self.rx_buffer) < 5:
                break
            
            length = self.rx_buffer[1]
            frame_length = 4 + length  # 帧头 + 长度 + 数据 + 校验 + 帧尾
            
            if len(self.rx_buffer) < frame_length:
                break
            
            # 提取完整帧
            frame_data = bytes(self.rx_buffer[:frame_length])
            self.rx_buffer = self.rx_buffer[frame_length:]
            
            # 验证并转发帧
            if validate_frame(frame_data):
                command = frame_data[2]
                data = frame_data[3:3+length-1] if length > 1 else b''
                
                # 创建消息并转发给客户端
                message = ProxyMessage(
                    msg_type='serial_data',
                    client_id='proxy',
                    data=frame_data,
                    command=command
                )
                
                self._forward_to_clients(message)

                # 只对特定命令自动发送ACK，关机请求由关机守护进程处理
                if command != MessageType.ACK and command != MessageType.SHUTDOWN_REQ:
                    self._send_auto_ack()
            else:
                self.logger.warning(f"无效帧: {frame_data.hex(' ').upper()}")
    
    def _forward_to_clients(self, message: ProxyMessage):
        """转发消息给客户端"""
        with self.clients_lock:
            for client_id, client_socket in list(self.clients.items()):
                try:
                    # 检查过滤器
                    filters = self.client_filters.get(client_id, [])
                    if filters and message.command not in filters:
                        continue
                    
                    # 发送消息
                    self._send_to_client(client_socket, message)
                
                except Exception as e:
                    self.logger.error(f"转发消息给客户端 {client_id} 失败: {e}")
                    # 移除失效的客户端
                    self.clients.pop(client_id, None)
                    self.client_filters.pop(client_id, None)
    
    def _send_to_client(self, client_socket: socket.socket, message: ProxyMessage):
        """发送消息给客户端"""
        try:
            data = message.to_json().encode('utf-8') + b'\n'
            client_socket.send(data)
        except Exception as e:
            raise e
    
    def _send_auto_ack(self):
        """自动发送ACK应答"""
        try:
            ack_frame = create_ack_frame()
            ack_data = ack_frame.to_bytes()
            
            if self.serial and self.serial.is_open:
                self.serial.write(ack_data)
                self.serial.flush()
                self.logger.debug(f"发送ACK: {ack_data.hex(' ').upper()}")
        
        except Exception as e:
            self.logger.error(f"发送ACK失败: {e}")
    
    def run(self):
        """运行守护进程"""
        if not self.start():
            return 1
        
        try:
            # 保持运行
            while self.running:
                time.sleep(1)
        
        except KeyboardInterrupt:
            self.logger.info("收到中断信号")
        
        finally:
            self.stop()
        
        return 0


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='N100串口代理守护进程')
    parser.add_argument('--port', default='/dev/ttyS4', help='串口设备路径')
    parser.add_argument('--socket', default='/tmp/n100_serial_proxy.sock', help='Unix Socket路径')
    parser.add_argument('--log', default='/var/log/n100_serial_proxy.log', help='日志文件路径')
    
    args = parser.parse_args()
    
    # 创建并运行守护进程
    daemon = SerialProxyDaemon(
        serial_port=args.port,
        socket_path=args.socket,
        log_file=args.log
    )
    
    return daemon.run()


if __name__ == "__main__":
    sys.exit(main())
