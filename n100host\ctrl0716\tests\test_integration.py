#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制系统集成测试
测试整个系统的集成功能
"""

import unittest
import sys
import os
import time
import subprocess
import tempfile
from unittest.mock import Mock, patch, MagicMock

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))

from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod
from protocol import validate_frame, STANDARD_FRAMES


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试"""
    
    def test_protocol_controller_integration(self):
        """测试协议与控制器集成"""
        controller = N100PowerController(port='/dev/test_port', use_manager=False, use_proxy=False)
        
        # 测试控制器创建的帧与标准帧匹配
        test_cases = [
            (controller._create_frame(0x01, bytes([0x00])), STANDARD_FRAMES['led_normal']),
            (controller._create_frame(0x01, bytes([0x01])), STANDARD_FRAMES['led_breath']),
            (controller._create_frame(0x02, bytes([0x01])), STANDARD_FRAMES['breath_1s']),
            (controller._create_frame(0x02, bytes([0x03])), STANDARD_FRAMES['breath_3s']),
            (controller._create_frame(0x02, bytes([0x05])), STANDARD_FRAMES['breath_5s']),
            (controller._create_frame(0x03), STANDARD_FRAMES['shutdown_success']),
        ]
        
        for created_frame, standard_frame in test_cases:
            self.assertEqual(created_frame, standard_frame, 
                f"创建的帧与标准帧不匹配: {created_frame.hex()} != {standard_frame.hex()}")
    
    def test_all_standard_frames_valid(self):
        """测试所有标准帧都有效"""
        for name, frame_data in STANDARD_FRAMES.items():
            with self.subTest(frame=name):
                self.assertTrue(validate_frame(frame_data), 
                    f"标准帧无效: {name} = {frame_data.hex()}")


class TestCommandLineInterface(unittest.TestCase):
    """命令行接口测试"""
    
    def setUp(self):
        """测试前准备"""
        self.cli_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'src', 'power_ctrl_cli.py'
        )
    
    def test_cli_help(self):
        """测试命令行帮助"""
        try:
            result = subprocess.run(
                [sys.executable, self.cli_path, '--help'],
                capture_output=True,
                text=True,
                timeout=10
            )
            self.assertEqual(result.returncode, 0)
            self.assertIn('usage:', result.stdout.lower())
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.skipTest("CLI测试需要实际的CLI文件")
    
    def test_cli_invalid_command(self):
        """测试无效命令"""
        try:
            result = subprocess.run(
                [sys.executable, self.cli_path, 'invalid_command'],
                capture_output=True,
                text=True,
                timeout=10
            )
            self.assertNotEqual(result.returncode, 0)
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.skipTest("CLI测试需要实际的CLI文件")


class TestProxyIntegration(unittest.TestCase):
    """代理集成测试"""
    
    @patch('socket.socket')
    def test_proxy_client_creation(self, mock_socket):
        """测试代理客户端创建"""
        # 模拟socket连接
        mock_socket_instance = MagicMock()
        mock_socket.return_value = mock_socket_instance
        
        # 测试代理模式控制器
        controller = N100PowerController(
            port='/dev/test_port',
            use_proxy=True
        )
        
        # 验证代理客户端被创建
        self.assertTrue(controller.use_proxy)
        self.assertIsNotNone(controller.client_id)


class TestErrorRecovery(unittest.TestCase):
    """错误恢复测试"""
    
    def test_connection_retry(self):
        """测试连接重试"""
        controller = N100PowerController(
            port='/dev/nonexistent_port',
            max_retries=2,
            use_manager=False,
            use_proxy=False
        )
        
        # 连接应该失败
        result = controller.connect()
        self.assertFalse(result)
        self.assertFalse(controller.is_connected)
    
    @patch('serial.Serial')
    def test_communication_timeout_recovery(self, mock_serial):
        """测试通信超时恢复"""
        # 模拟串口，但不返回ACK
        mock_serial_instance = MagicMock()
        mock_serial.return_value = mock_serial_instance
        mock_serial_instance.is_open = True
        mock_serial_instance.in_waiting = 0
        mock_serial_instance.read.return_value = b''  # 不返回ACK
        
        controller = N100PowerController(
            port='/dev/test_port',
            timeout=0.1,  # 短超时
            max_retries=2,
            use_manager=False,
            use_proxy=False
        )
        
        # 连接
        self.assertTrue(controller.connect())
        
        # 发送命令（应该超时）
        result = controller.set_led_normal()
        # 由于超时，结果可能是False，但不应该抛出异常
        self.assertIsInstance(result, bool)
        
        controller.disconnect()


class TestPerformance(unittest.TestCase):
    """性能测试"""
    
    @patch('serial.Serial')
    def test_command_execution_time(self, mock_serial):
        """测试命令执行时间"""
        # 模拟快速ACK响应
        mock_serial_instance = MagicMock()
        mock_serial.return_value = mock_serial_instance
        mock_serial_instance.is_open = True
        mock_serial_instance.in_waiting = 5
        mock_serial_instance.read.return_value = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])  # ACK帧
        
        controller = N100PowerController(
            port='/dev/test_port',
            timeout=1.0,
            max_retries=1,
            use_manager=False,
            use_proxy=False
        )
        
        self.assertTrue(controller.connect())
        
        # 测试命令执行时间
        start_time = time.time()
        controller.set_led_normal()
        end_time = time.time()
        
        execution_time = end_time - start_time
        # 命令执行时间应该在合理范围内（小于2秒）
        self.assertLess(execution_time, 2.0, "命令执行时间过长")
        
        controller.disconnect()
    
    @patch('serial.Serial')
    def test_multiple_commands_performance(self, mock_serial):
        """测试多命令性能"""
        # 模拟串口
        mock_serial_instance = MagicMock()
        mock_serial.return_value = mock_serial_instance
        mock_serial_instance.is_open = True
        mock_serial_instance.in_waiting = 5
        mock_serial_instance.read.return_value = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
        
        controller = N100PowerController(
            port='/dev/test_port',
            timeout=0.5,
            max_retries=1,
            use_manager=False,
            use_proxy=False
        )
        
        self.assertTrue(controller.connect())
        
        # 执行多个命令
        commands = [
            controller.set_led_normal,
            controller.set_led_breath,
            controller.set_breath_1s,
            controller.set_breath_3s,
            controller.set_breath_5s,
        ]
        
        start_time = time.time()
        for cmd in commands:
            cmd()
        end_time = time.time()
        
        total_time = end_time - start_time
        avg_time = total_time / len(commands)
        
        # 平均每个命令执行时间应该合理
        self.assertLess(avg_time, 1.0, f"平均命令执行时间过长: {avg_time:.2f}秒")
        
        controller.disconnect()


class TestDataIntegrity(unittest.TestCase):
    """数据完整性测试"""
    
    def test_frame_roundtrip(self):
        """测试帧往返转换"""
        from protocol import ProtocolFrame, create_frame
        
        # 创建各种帧
        test_frames = [
            create_frame(0x01, bytes([0x00])),  # LED正常
            create_frame(0x01, bytes([0x01])),  # LED呼吸
            create_frame(0x02, bytes([0x03])),  # 3秒呼吸
            create_frame(0x03),                 # 关机成功
            create_frame(0x80),                 # ACK
        ]
        
        for original_frame in test_frames:
            with self.subTest(frame=original_frame.command):
                # 转换为字节
                frame_bytes = original_frame.to_bytes()
                
                # 验证帧有效性
                self.assertTrue(validate_frame(frame_bytes))
                
                # 从字节重建帧
                rebuilt_frame = ProtocolFrame.from_bytes(frame_bytes)
                self.assertIsNotNone(rebuilt_frame)
                
                # 验证数据一致性
                self.assertEqual(rebuilt_frame.command, original_frame.command)
                self.assertEqual(rebuilt_frame.data, original_frame.data)
                self.assertEqual(rebuilt_frame.checksum, original_frame.checksum)
    
    def test_checksum_integrity(self):
        """测试校验和完整性"""
        from protocol import calculate_checksum
        
        # 测试校验和计算的一致性
        test_data = [
            bytes([0x01, 0x00]),
            bytes([0x01, 0x01]),
            bytes([0x02, 0x01]),
            bytes([0x02, 0x03]),
            bytes([0x02, 0x05]),
            bytes([0x03]),
            bytes([0x80]),
        ]
        
        for data in test_data:
            with self.subTest(data=data.hex()):
                # 多次计算校验和应该得到相同结果
                checksum1 = calculate_checksum(data)
                checksum2 = calculate_checksum(data)
                self.assertEqual(checksum1, checksum2, "校验和计算不一致")


class TestSystemCompatibility(unittest.TestCase):
    """系统兼容性测试"""
    
    def test_python_version_compatibility(self):
        """测试Python版本兼容性"""
        # 检查Python版本
        self.assertGreaterEqual(sys.version_info, (3, 6), "需要Python 3.6或更高版本")
    
    def test_module_imports(self):
        """测试模块导入"""
        # 测试所有必要模块都能正确导入
        try:
            import serial
            import time
            import threading
            import socket
            import json
            import logging
            import subprocess
        except ImportError as e:
            self.fail(f"必要模块导入失败: {e}")
    
    def test_enum_compatibility(self):
        """测试枚举兼容性"""
        # 测试枚举值
        self.assertEqual(LEDMode.NORMAL, 0)
        self.assertEqual(LEDMode.BREATH, 1)
        self.assertEqual(BreathPeriod.PERIOD_1S, 1)
        self.assertEqual(BreathPeriod.PERIOD_3S, 3)
        self.assertEqual(BreathPeriod.PERIOD_5S, 5)


def run_tests():
    """运行所有集成测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestSystemIntegration,
        TestCommandLineInterface,
        TestProxyIntegration,
        TestErrorRecovery,
        TestPerformance,
        TestDataIntegrity,
        TestSystemCompatibility,
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    print("N100电源控制系统集成测试")
    print("=" * 50)
    
    success = run_tests()
    
    if success:
        print("\n🎉 所有集成测试通过！")
        exit(0)
    else:
        print("\n❌ 部分集成测试失败！")
        exit(1)
