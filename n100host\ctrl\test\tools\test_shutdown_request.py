#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
关机请求测试工具
测试关机请求的发送和接收
"""

import os
import sys
import time
import socket
import threading
import argparse

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', '..', 'src')
sys.path.insert(0, src_dir)

try:
    from serial_proxy_client import SerialProxyClient, ProxyMessage
except ImportError as e:
    print(f"错误: 无法导入代理客户端模块: {e}")
    sys.exit(1)


def test_direct_serial():
    """测试直接串口通信"""
    print("=== 测试直接串口通信 ===")
    
    try:
        import serial
        
        # 尝试打开串口
        ser = serial.Serial('/dev/ttyS4', 115200, timeout=2)
        print("✅ 串口打开成功")
        
        # 发送关机请求
        shutdown_request = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
        print(f"发送关机请求: {shutdown_request.hex(' ').upper()}")
        
        ser.write(shutdown_request)
        ser.flush()
        
        # 等待应答
        print("等待应答...")
        start_time = time.time()
        
        while time.time() - start_time < 5:  # 等待5秒
            if ser.in_waiting > 0:
                response = ser.read(ser.in_waiting)
                print(f"收到应答: {response.hex(' ').upper()}")
                
                # 检查是否为ACK
                expected_ack = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
                if expected_ack in response:
                    print("✅ 收到正确的ACK应答")
                    ser.close()
                    return True
                
            time.sleep(0.1)
        
        print("❌ 5秒内未收到应答")
        ser.close()
        return False
        
    except Exception as e:
        print(f"❌ 直接串口测试失败: {e}")
        return False


def test_proxy_client():
    """测试代理客户端"""
    print("\n=== 测试代理客户端 ===")
    
    try:
        # 创建代理客户端
        client = SerialProxyClient("shutdown_test")
        
        # 设置回调
        received_messages = []
        
        def on_message(message):
            received_messages.append(message)
            print(f"收到消息: 类型={message.msg_type}, 命令=0x{message.command:02X}")
            
            # 如果是ACK，打印详细信息
            if message.command == 0x80:
                print(f"✅ 收到ACK应答: {message.data.hex(' ').upper()}")
        
        client.set_message_callback(on_message)
        
        # 连接到代理
        if client.connect():
            print("✅ 连接到代理服务成功")
            
            # 发送关机请求
            shutdown_request = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
            print(f"发送关机请求: {shutdown_request.hex(' ').upper()}")
            
            if client.send_data(shutdown_request):
                print("✅ 关机请求发送成功")
                
                # 等待应答
                print("等待应答...")
                start_time = time.time()
                
                while time.time() - start_time < 5:  # 等待5秒
                    if received_messages:
                        print(f"✅ 收到 {len(received_messages)} 条消息")
                        client.disconnect()
                        return True
                    
                    time.sleep(0.1)
                
                print("❌ 5秒内未收到应答")
                client.disconnect()
                return False
            else:
                print("❌ 关机请求发送失败")
                client.disconnect()
                return False
        else:
            print("❌ 连接到代理服务失败")
            return False
            
    except Exception as e:
        print(f"❌ 代理客户端测试失败: {e}")
        return False


def check_proxy_socket():
    """检查代理Socket"""
    print("\n=== 检查代理Socket ===")
    
    socket_path = '/tmp/n100_serial_proxy.sock'
    
    if os.path.exists(socket_path):
        print(f"✅ 代理Socket存在: {socket_path}")
        
        # 检查权限
        try:
            stat_info = os.stat(socket_path)
            print(f"权限: {oct(stat_info.st_mode)[-3:]}")
            
            # 尝试连接
            sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
            sock.settimeout(2)
            
            try:
                sock.connect(socket_path)
                print("✅ 可以连接到Socket")
                sock.close()
                return True
            except Exception as e:
                print(f"❌ 无法连接到Socket: {e}")
                return False
                
        except Exception as e:
            print(f"❌ 检查Socket失败: {e}")
            return False
    else:
        print(f"❌ 代理Socket不存在: {socket_path}")
        return False


def check_shutdown_daemon():
    """检查关机守护进程"""
    print("\n=== 检查关机守护进程 ===")
    
    try:
        import subprocess
        
        # 检查服务状态
        result = subprocess.run(['systemctl', 'status', 'n100-shutdown-proxy'], 
                              capture_output=True, text=True)
        
        if 'Active: active (running)' in result.stdout:
            print("✅ 关机守护进程运行中")
            
            # 获取最新日志
            log_result = subprocess.run(['journalctl', '-u', 'n100-shutdown-proxy', '-n', '10', '--no-pager'], 
                                      capture_output=True, text=True)
            
            print("\n最新日志:")
            print(log_result.stdout)
            
            if "ERROR" in log_result.stdout:
                print("⚠️ 日志中有错误")
                return False
            else:
                return True
        else:
            print("❌ 关机守护进程未运行")
            print(result.stdout)
            return False
            
    except Exception as e:
        print(f"❌ 检查守护进程失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='关机请求测试工具')
    parser.add_argument('--direct', action='store_true', help='使用直接串口模式')
    parser.add_argument('--proxy', action='store_true', help='使用代理模式')
    parser.add_argument('--all', action='store_true', help='运行所有测试')
    
    args = parser.parse_args()
    
    # 如果没有指定参数，默认运行所有测试
    if not (args.direct or args.proxy or args.all):
        args.all = True
    
    print("关机请求测试工具")
    print("=" * 40)
    
    results = {}
    
    # 检查代理Socket
    if args.proxy or args.all:
        results['proxy_socket'] = check_proxy_socket()
    
    # 检查关机守护进程
    if args.proxy or args.all:
        results['shutdown_daemon'] = check_shutdown_daemon()
    
    # 测试直接串口
    if args.direct or args.all:
        results['direct_serial'] = test_direct_serial()
    
    # 测试代理客户端
    if args.proxy or args.all:
        results['proxy_client'] = test_proxy_client()
    
    # 显示结果
    print("\n=== 测试结果 ===")
    for test, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test}: {status}")
    
    # 提供解决方案
    print("\n=== 解决方案 ===")
    
    if 'proxy_socket' in results and not results['proxy_socket']:
        print("🔧 代理Socket问题:")
        print("1. 重启代理服务: sudo systemctl restart n100-serial-proxy")
        print("2. 检查日志: journalctl -u n100-serial-proxy -f")
    
    if 'shutdown_daemon' in results and not results['shutdown_daemon']:
        print("🔧 关机守护进程问题:")
        print("1. 重启守护进程: sudo systemctl restart n100-shutdown-proxy")
        print("2. 检查日志: journalctl -u n100-shutdown-proxy -f")
    
    if 'direct_serial' in results and not results['direct_serial']:
        print("🔧 直接串口问题:")
        print("1. 检查串口权限: sudo chmod 666 /dev/ttyS4")
        print("2. 检查硬件连接")
        print("3. 检查电源板状态")
    
    if 'proxy_client' in results and not results['proxy_client']:
        print("🔧 代理客户端问题:")
        print("1. 确保代理服务运行: sudo systemctl restart n100-serial-proxy")
        print("2. 检查Socket权限: sudo chmod 666 /tmp/n100_serial_proxy.sock")
    
    # 如果所有测试都通过
    if all(results.values()):
        print("🎉 所有测试通过！系统工作正常")
    
    return 0 if all(results.values()) else 1


if __name__ == "__main__":
    sys.exit(main())
