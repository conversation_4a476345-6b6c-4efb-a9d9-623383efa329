#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的串口代理架构测试
在Windows环境下测试代理架构的核心功能
"""

import os
import sys
import time
import threading
from typing import Dict, List

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', '..', 'src')
test_dir = os.path.join(current_dir, '..')

sys.path.insert(0, src_dir)
sys.path.insert(0, test_dir)

try:
    from memory_serial_simulator import MemorySerial, PowerBoardSimulator
except ImportError as e:
    print(f"错误: 无法导入模拟器模块: {e}")
    sys.exit(1)


class SimpleProxyDaemon:
    """简化的串口代理守护进程（用于测试）"""
    
    def __init__(self, serial_device):
        self.serial = serial_device
        self.clients = {}
        self.running = False
        self.rx_buffer = bytearray()
        self.clients_lock = threading.Lock()
        
    def start(self):
        """启动代理服务"""
        self.running = True
        # 启动串口读取线程
        self.read_thread = threading.Thread(target=self._read_loop, daemon=True)
        self.read_thread.start()
        return True
    
    def stop(self):
        """停止代理服务"""
        self.running = False
    
    def register_client(self, client_id, callback):
        """注册客户端"""
        with self.clients_lock:
            self.clients[client_id] = callback
        print(f"客户端 {client_id} 已注册")
    
    def unregister_client(self, client_id):
        """注销客户端"""
        with self.clients_lock:
            self.clients.pop(client_id, None)
        print(f"客户端 {client_id} 已注销")
    
    def send_data(self, data):
        """发送数据到串口"""
        if self.serial:
            self.serial.write(data)
            print(f"代理发送: {data.hex(' ').upper()}")
            return True
        return False
    
    def _read_loop(self):
        """串口读取循环"""
        while self.running:
            try:
                if self.serial and self.serial.in_waiting > 0:
                    data = self.serial.read(self.serial.in_waiting)
                    if data:
                        print(f"代理接收: {data.hex(' ').upper()}")
                        self._forward_to_clients(data)
                
                time.sleep(0.01)
            except Exception as e:
                print(f"读取异常: {e}")
                break
    
    def _forward_to_clients(self, data):
        """转发数据给所有客户端"""
        with self.clients_lock:
            for client_id, callback in self.clients.items():
                try:
                    callback(data)
                except Exception as e:
                    print(f"转发给客户端 {client_id} 失败: {e}")


class SimpleProxyClient:
    """简化的串口代理客户端"""
    
    def __init__(self, client_id, proxy_daemon):
        self.client_id = client_id
        self.proxy = proxy_daemon
        self.message_callback = None
        self.connected = False
    
    def connect(self):
        """连接到代理"""
        if self.proxy:
            self.proxy.register_client(self.client_id, self._on_data)
            self.connected = True
            return True
        return False
    
    def disconnect(self):
        """断开连接"""
        if self.proxy and self.connected:
            self.proxy.unregister_client(self.client_id)
            self.connected = False
    
    def send_data(self, data):
        """发送数据"""
        if self.proxy and self.connected:
            return self.proxy.send_data(data)
        return False
    
    def set_message_callback(self, callback):
        """设置消息回调"""
        self.message_callback = callback
    
    def _on_data(self, data):
        """处理接收到的数据"""
        if self.message_callback:
            self.message_callback(data)


class SimpleProxyTester:
    """简化的代理测试器"""
    
    def __init__(self):
        # 创建内存串口对
        self.n100_serial = MemorySerial("N100")
        self.power_serial = MemorySerial("PowerBoard")
        self.n100_serial.connect_peer(self.power_serial)
        
        # 创建电源板模拟器
        self.power_simulator = PowerBoardSimulator(self.power_serial)
        
        # 创建代理守护进程
        self.proxy_daemon = SimpleProxyDaemon(self.n100_serial)
        
        # 测试客户端
        self.clients = []
    
    def test_single_client(self):
        """测试单个客户端"""
        print("\n=== 测试单个客户端 ===")
        
        try:
            # 创建客户端
            client = SimpleProxyClient("test_client_1", self.proxy_daemon)
            
            # 设置回调
            received_data = []
            
            def on_data(data):
                received_data.append(data)
                print(f"客户端1收到: {data.hex(' ').upper()}")
            
            client.set_message_callback(on_data)
            
            # 连接
            if client.connect():
                print("✅ 客户端1连接成功")
                
                # 发送测试数据
                test_data = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
                if client.send_data(test_data):
                    print(f"✅ 客户端1发送成功")
                    
                    # 等待响应
                    time.sleep(1)
                    
                    if received_data:
                        print(f"✅ 客户端1收到 {len(received_data)} 条消息")
                        return True
                    else:
                        print("❌ 客户端1未收到响应")
                        return False
                else:
                    print("❌ 客户端1发送失败")
                    return False
            else:
                print("❌ 客户端1连接失败")
                return False
                
        except Exception as e:
            print(f"❌ 单客户端测试异常: {e}")
            return False
        finally:
            if 'client' in locals():
                client.disconnect()
    
    def test_multiple_clients(self):
        """测试多个客户端"""
        print("\n=== 测试多个客户端 ===")
        
        clients = []
        
        try:
            # 创建多个客户端
            for i in range(3):
                client = SimpleProxyClient(f"test_client_{i+2}", self.proxy_daemon)
                
                # 设置回调
                client_data = []
                
                def make_callback(client_id, data_list):
                    def on_data(data):
                        data_list.append(data)
                        print(f"客户端{client_id}收到: {data.hex(' ').upper()}")
                    return on_data
                
                client.set_message_callback(make_callback(i+2, client_data))
                
                # 连接
                if client.connect():
                    print(f"✅ 客户端{i+2}连接成功")
                    clients.append((client, client_data))
                else:
                    print(f"❌ 客户端{i+2}连接失败")
                    return False
            
            # 等待所有客户端连接完成
            time.sleep(0.5)
            
            # 发送测试数据
            test_data = bytes([0xAA, 0x02, 0x01, 0x01, 0xFE, 0x55])
            
            # 通过第一个客户端发送
            if clients[0][0].send_data(test_data):
                print("✅ 发送测试数据成功")
                
                # 等待响应
                time.sleep(1)
                
                # 检查所有客户端是否都收到了消息
                all_received = True
                for i, (client, data_list) in enumerate(clients):
                    if data_list:
                        print(f"✅ 客户端{i+2}收到 {len(data_list)} 条消息")
                    else:
                        print(f"❌ 客户端{i+2}未收到消息")
                        all_received = False
                
                return all_received
            else:
                print("❌ 发送测试数据失败")
                return False
            
        except Exception as e:
            print(f"❌ 多客户端测试异常: {e}")
            return False
        finally:
            # 断开所有客户端
            for client, _ in clients:
                client.disconnect()
    
    def test_shutdown_simulation(self):
        """测试关机模拟"""
        print("\n=== 测试关机模拟 ===")
        
        try:
            # 创建关机监听客户端
            shutdown_client = SimpleProxyClient("shutdown_listener", self.proxy_daemon)
            
            shutdown_received = []
            
            def on_shutdown_data(data):
                # 检查是否为关机请求 (AA 01 13 ED 55)
                if len(data) >= 5 and data[0] == 0xAA and data[2] == 0x13:
                    shutdown_received.append(data)
                    print("收到关机请求，发送ACK...")
                    
                    # 发送ACK
                    ack_data = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
                    shutdown_client.send_data(ack_data)
            
            shutdown_client.set_message_callback(on_shutdown_data)
            
            # 连接
            if shutdown_client.connect():
                print("✅ 关机监听客户端连接成功")
                
                # 模拟电源板发送关机请求
                print("模拟电源板发送关机请求...")
                self.power_simulator.send_shutdown_request()
                
                # 等待处理
                time.sleep(1)
                
                if shutdown_received:
                    print("✅ 关机请求处理成功")
                    return True
                else:
                    print("❌ 未收到关机请求")
                    return False
            else:
                print("❌ 关机监听客户端连接失败")
                return False
                
        except Exception as e:
            print(f"❌ 关机模拟测试异常: {e}")
            return False
        finally:
            if 'shutdown_client' in locals():
                shutdown_client.disconnect()
    
    def run_all_tests(self):
        """运行所有测试"""
        print("简化串口代理架构测试")
        print("=" * 40)
        
        results = {}
        
        try:
            # 启动电源板模拟器
            self.power_simulator.start()
            time.sleep(0.5)
            
            # 启动代理守护进程
            if not self.proxy_daemon.start():
                print("❌ 代理守护进程启动失败")
                return False
            
            print("✅ 代理守护进程启动成功")
            time.sleep(0.5)
            
            # 1. 测试单个客户端
            results['single_client'] = self.test_single_client()
            
            # 2. 测试多个客户端
            results['multiple_clients'] = self.test_multiple_clients()
            
            # 3. 测试关机模拟
            results['shutdown_simulation'] = self.test_shutdown_simulation()
            
            # 显示结果
            print(f"\n=== 测试结果总结 ===")
            for test_name, result in results.items():
                status = "✅ 通过" if result else "❌ 失败"
                print(f"{test_name}: {status}")
            
            # 显示统计信息
            print(f"\n=== 通信统计 ===")
            print(f"电源板接收命令: {self.power_simulator.stats['received_commands']}")
            print(f"电源板发送ACK: {self.power_simulator.stats['sent_acks']}")
            print(f"N100发送字节: {self.n100_serial.bytes_sent}")
            print(f"N100接收字节: {self.n100_serial.bytes_received}")
            
            all_passed = all(results.values())
            if all_passed:
                print("\n🎉 所有简化代理架构测试通过！")
                print("代理架构核心功能验证成功")
            else:
                print("\n❌ 部分简化代理架构测试失败")
            
            return all_passed
            
        except Exception as e:
            print(f"\n❌ 测试异常: {e}")
            return False
        finally:
            # 清理资源
            self.proxy_daemon.stop()
            self.power_simulator.stop()


def main():
    """主函数"""
    tester = SimpleProxyTester()
    
    try:
        success = tester.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n测试被中断")
        return 1


if __name__ == "__main__":
    sys.exit(main())
