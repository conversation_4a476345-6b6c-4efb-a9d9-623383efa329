#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
带DEBUG日志的关机流程测试
直接运行关机守护进程进行调试
"""

import os
import sys
import time
import threading
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

try:
    from serial_proxy_client import SerialProxyClient
    from serial_proxy_daemon import ProxyMessage, MessageType
    from n100_shutdown_daemon_proxy import N100ShutdownDaemonProxy
except ImportError as e:
    print(f"错误: 无法导入模块: {e}")
    sys.exit(1)


def setup_debug_logging():
    """设置DEBUG级别日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )


def test_shutdown_daemon_debug():
    """测试关机守护进程（DEBUG模式）"""
    print("=== 关机守护进程DEBUG测试 ===")
    
    # 设置DEBUG日志
    setup_debug_logging()
    
    # 创建关机守护进程实例
    daemon = N100ShutdownDaemonProxy()
    
    try:
        print("启动关机守护进程...")
        daemon.start()
        
        print("关机守护进程已启动，等待5秒...")
        time.sleep(5)
        
        print("现在发送关机请求...")
        
        # 创建测试客户端发送关机请求
        client = SerialProxyClient("debug_sender")
        
        if client.connect():
            shutdown_request = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
            print(f"发送关机请求: {shutdown_request.hex(' ').upper()}")
            
            if client.send_data(shutdown_request):
                print("✅ 关机请求发送成功")
            else:
                print("❌ 关机请求发送失败")
            
            client.disconnect()
        else:
            print("❌ 无法连接到串口代理")
        
        print("等待处理...")
        time.sleep(10)
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("停止关机守护进程...")
        daemon.stop()


def test_serial_proxy_debug():
    """测试串口代理连接和消息发送"""
    print("\n=== 串口代理连接测试 ===")
    
    setup_debug_logging()
    
    client = SerialProxyClient("debug_test")
    received_messages = []
    
    def on_message(message):
        received_messages.append(message)
        print(f"收到消息: 类型={message.msg_type}, 命令=0x{message.command:02X}, 数据={message.data.hex(' ').upper()}")
    
    def on_error(error_msg):
        print(f"客户端错误: {error_msg}")
    
    client.set_message_callback(on_message)
    client.set_error_callback(on_error)
    
    if client.connect():
        print("✅ 连接成功")
        
        # 发送关机请求
        shutdown_request = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
        print(f"发送关机请求: {shutdown_request.hex(' ').upper()}")
        
        if client.send_data(shutdown_request):
            print("✅ 发送成功")
        else:
            print("❌ 发送失败")
        
        print("等待响应...")
        time.sleep(5)
        
        print(f"收到 {len(received_messages)} 条消息")
        
        client.disconnect()
    else:
        print("❌ 连接失败")


def main():
    """主函数"""
    print("N100关机流程DEBUG测试")
    print("=" * 50)
    
    print("选择测试模式:")
    print("1. 测试串口代理连接和消息发送")
    print("2. 测试关机守护进程（会停止系统服务）")
    print("3. 退出")
    
    try:
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            test_serial_proxy_debug()
        elif choice == "2":
            print("\n⚠️  警告: 这将停止系统的关机守护进程服务")
            confirm = input("确认继续? (y/N): ").strip().lower()
            if confirm == 'y':
                # 停止系统服务
                os.system("sudo systemctl stop n100-shutdown-proxy")
                test_shutdown_daemon_debug()
                # 重启系统服务
                os.system("sudo systemctl start n100-shutdown-proxy")
            else:
                print("取消测试")
        elif choice == "3":
            print("退出")
        else:
            print("无效选择")
    
    except KeyboardInterrupt:
        print("\n测试被中断")
    
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    main()
