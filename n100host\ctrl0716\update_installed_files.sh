#!/bin/bash

# 更新安装的文件脚本
# 将修改后的源文件复制到系统安装目录

set -e

echo "=== 更新N100系统安装文件 ==="
echo "更新时间: $(date)"
echo ""

# 检查安装目录是否存在
INSTALL_DIR="/opt/n100/ctrl"
if [ ! -d "$INSTALL_DIR" ]; then
    echo "❌ 安装目录不存在: $INSTALL_DIR"
    echo "请先运行安装脚本"
    exit 1
fi

echo "1. 停止服务..."
sudo systemctl stop n100-shutdown-proxy n100-serial-proxy
echo "✅ 服务已停止"

echo ""
echo "2. 备份现有文件..."
BACKUP_DIR="/opt/n100/ctrl_backup_$(date +%Y%m%d_%H%M%S)"
sudo mkdir -p "$BACKUP_DIR"
sudo cp -r "$INSTALL_DIR"/* "$BACKUP_DIR"/ 2>/dev/null || true
echo "✅ 备份完成: $BACKUP_DIR"

echo ""
echo "3. 更新源文件..."

# 更新的文件列表
FILES_TO_UPDATE=(
    "serial_proxy_daemon.py"
    "n100_shutdown_daemon_proxy.py"
    "serial_proxy_client.py"
    "protocol.py"
    "n100_power_ctrl.py"
    "power_ctrl_cli.py"
)

for file in "${FILES_TO_UPDATE[@]}"; do
    if [ -f "src/$file" ]; then
        echo "  更新 $file..."
        sudo cp "src/$file" "$INSTALL_DIR/"
        sudo chmod +x "$INSTALL_DIR/$file"
        echo "  ✅ $file 已更新"
    else
        echo "  ⚠️  源文件不存在: src/$file"
    fi
done

echo ""
echo "4. 验证文件..."
for file in "${FILES_TO_UPDATE[@]}"; do
    if [ -f "$INSTALL_DIR/$file" ]; then
        echo "  ✅ $file 存在"
    else
        echo "  ❌ $file 缺失"
    fi
done

echo ""
echo "5. 设置权限..."
sudo chown -R root:root "$INSTALL_DIR"
sudo chmod +x "$INSTALL_DIR"/*.py
echo "✅ 权限设置完成"

echo ""
echo "6. 启动服务..."
sudo systemctl start n100-serial-proxy
sleep 2

if systemctl is-active --quiet n100-serial-proxy; then
    echo "✅ 串口代理服务启动成功"
else
    echo "❌ 串口代理服务启动失败"
    sudo journalctl -u n100-serial-proxy -n 5
fi

sudo systemctl start n100-shutdown-proxy
sleep 2

if systemctl is-active --quiet n100-shutdown-proxy; then
    echo "✅ 关机守护进程启动成功"
else
    echo "❌ 关机守护进程启动失败"
    sudo journalctl -u n100-shutdown-proxy -n 5
fi

echo ""
echo "7. 检查服务状态..."
systemctl status n100-serial-proxy n100-shutdown-proxy --no-pager

echo ""
echo "8. 查看启动日志..."
echo "串口代理日志:"
sudo journalctl -u n100-serial-proxy --since "1 minute ago" --no-pager

echo ""
echo "关机守护进程日志:"
sudo journalctl -u n100-shutdown-proxy --since "1 minute ago" --no-pager

echo ""
echo "🎉 文件更新完成！"
echo ""
echo "现在可以运行测试:"
echo "  python3 simple_shutdown_test.py"
echo "  python3 test_shutdown_daemon.py"
echo ""
echo "如果需要恢复，备份位置: $BACKUP_DIR"
