#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复后的关机流程测试
验证关机请求的完整流程
"""

import os
import sys
import time
import subprocess
import threading

def send_shutdown_request():
    """发送关机请求"""
    print("=== 发送关机请求 ===")
    
    try:
        # 发送关机请求到串口
        shutdown_request = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
        print(f"发送关机请求: {shutdown_request.hex(' ').upper()}")
        
        # 使用echo命令发送
        cmd = f"echo -ne '\\xAA\\x01\\x13\\xED\\x55' | sudo tee /dev/ttyS4"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 关机请求发送成功")
            return True
        else:
            print(f"❌ 关机请求发送失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 发送关机请求异常: {e}")
        return False

def monitor_logs():
    """监控服务日志"""
    print("\n=== 监控服务日志 ===")
    
    def monitor_service(service_name):
        """监控单个服务的日志"""
        try:
            cmd = ['journalctl', '-u', service_name, '-f', '--no-pager']
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            print(f"开始监控 {service_name} 日志...")
            
            for line in iter(process.stdout.readline, ''):
                if line.strip():
                    print(f"[{service_name}] {line.strip()}")
                    
                    # 检查关键日志
                    if "收到电源板关机请求" in line:
                        print(f"✅ {service_name}: 收到关机请求")
                    elif "已发送ACK应答" in line:
                        print(f"✅ {service_name}: 发送ACK应答")
                    elif "执行关机命令" in line:
                        print(f"✅ {service_name}: 执行关机命令")
                    elif "已发送关机成功消息" in line:
                        print(f"✅ {service_name}: 发送关机成功消息")
                    elif "ERROR" in line:
                        print(f"❌ {service_name}: 错误 - {line.strip()}")
        
        except Exception as e:
            print(f"监控 {service_name} 异常: {e}")
    
    # 启动监控线程
    services = ['n100-serial-proxy', 'n100-shutdown-proxy']
    threads = []
    
    for service in services:
        thread = threading.Thread(target=monitor_service, args=(service,), daemon=True)
        thread.start()
        threads.append(thread)
    
    return threads

def check_ack_response():
    """检查ACK应答"""
    print("\n=== 检查ACK应答 ===")
    
    try:
        import serial
        
        # 打开串口监听ACK
        ser = serial.Serial('/dev/ttyS4', 115200, timeout=1)
        print("✅ 串口打开成功，开始监听ACK...")
        
        start_time = time.time()
        
        while time.time() - start_time < 10:  # 监听10秒
            if ser.in_waiting > 0:
                data = ser.read(ser.in_waiting)
                print(f"收到数据: {data.hex(' ').upper()}")
                
                # 检查ACK应答
                expected_ack = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
                if expected_ack in data:
                    print("✅ 收到ACK应答")
                    ser.close()
                    return True
                
                # 检查关机成功消息
                shutdown_success = bytes([0xAA, 0x01, 0x03, 0xFD, 0x55])
                if shutdown_success in data:
                    print("✅ 收到关机成功消息")
            
            time.sleep(0.1)
        
        print("❌ 10秒内未收到ACK应答")
        ser.close()
        return False
        
    except Exception as e:
        print(f"❌ 检查ACK应答失败: {e}")
        return False

def restart_services():
    """重启服务"""
    print("=== 重启服务 ===")
    
    services = ['n100-serial-proxy', 'n100-shutdown-proxy']
    
    for service in services:
        try:
            print(f"重启 {service}...")
            result = subprocess.run(['sudo', 'systemctl', 'restart', service], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {service} 重启成功")
            else:
                print(f"❌ {service} 重启失败: {result.stderr}")
                return False
            
            time.sleep(1)  # 等待服务启动
            
        except Exception as e:
            print(f"❌ 重启 {service} 异常: {e}")
            return False
    
    return True

def check_services_status():
    """检查服务状态"""
    print("\n=== 检查服务状态 ===")
    
    services = ['n100-serial-proxy', 'n100-shutdown-proxy']
    
    for service in services:
        try:
            result = subprocess.run(['systemctl', 'is-active', service], 
                                  capture_output=True, text=True)
            
            status = result.stdout.strip()
            if status == 'active':
                print(f"✅ {service}: 运行中")
            else:
                print(f"❌ {service}: {status}")
                return False
                
        except Exception as e:
            print(f"❌ 检查 {service} 状态失败: {e}")
            return False
    
    return True

def main():
    """主函数"""
    print("修复后的关机流程测试")
    print("=" * 40)
    
    # 1. 重启服务
    if not restart_services():
        print("❌ 服务重启失败")
        return 1
    
    # 2. 检查服务状态
    if not check_services_status():
        print("❌ 服务状态异常")
        return 1
    
    print("\n等待5秒让服务完全启动...")
    time.sleep(5)
    
    # 3. 启动日志监控
    log_threads = monitor_logs()
    
    print("\n等待3秒开始测试...")
    time.sleep(3)
    
    # 4. 在后台监听ACK
    def ack_monitor():
        check_ack_response()
    
    ack_thread = threading.Thread(target=ack_monitor, daemon=True)
    ack_thread.start()
    
    time.sleep(1)
    
    # 5. 发送关机请求
    if send_shutdown_request():
        print("\n✅ 关机请求已发送，等待处理...")
        
        # 等待10秒观察日志
        print("等待10秒观察日志和ACK应答...")
        time.sleep(10)
        
        print("\n=== 测试完成 ===")
        print("请检查上面的日志输出，确认:")
        print("1. ✅ 代理服务收到关机请求")
        print("2. ✅ 关机守护进程处理关机请求")
        print("3. ✅ 发送ACK应答")
        print("4. ✅ 执行关机流程")
        
        return 0
    else:
        print("❌ 关机请求发送失败")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)
