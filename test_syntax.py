#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试脚本语法的简单测试
"""

def test_import():
    """测试是否可以导入模块（不需要实际的串口）"""
    try:
        # 模拟serial模块
        import sys
        from unittest.mock import MagicMock
        
        # 创建模拟的serial模块
        mock_serial = MagicMock()
        mock_serial.Serial = MagicMock()
        sys.modules['serial'] = mock_serial
        
        # 现在尝试导入我们的模块
        import serial_comm
        
        print("✓ 模块导入成功")
        
        # 测试类是否可以实例化
        controller = serial_comm.PowerController(port="dummy", baudrate=115200)
        print("✓ PowerController类实例化成功")
        
        # 测试一些基本方法
        frame = controller.create_frame(0x01, b'\x02\x03')
        print(f"✓ create_frame方法工作正常: {frame.hex(' ').upper()}")
        
        print("✓ 所有基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始语法和基本功能测试...")
    success = test_import()
    if success:
        print("\n🎉 所有测试通过！代码可以正常运行。")
    else:
        print("\n❌ 测试失败，请检查代码。")
