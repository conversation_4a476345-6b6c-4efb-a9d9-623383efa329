#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
真实串口测试脚本
测试在真实环境中的串口通信
"""

import os
import sys
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    sys.exit(1)


def test_direct_communication():
    """测试直接串口通信（不使用串口管理器）"""
    print("=== 测试直接串口通信 ===")
    
    # 创建控制器，强制不使用串口管理器
    controller = N100PowerController(
        port='/dev/ttyS4',
        use_manager=False,  # 关键：不使用串口管理器
        max_retries=3,
        timeout=2.0
    )
    
    print("尝试直接连接串口...")
    if controller.connect():
        print("✅ 串口直接连接成功")
        
        # 测试LED控制
        print("\n1. 测试LED正常模式...")
        success = controller.set_led_mode(LEDMode.NORMAL)
        print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            time.sleep(1)
            
            print("\n2. 测试LED呼吸模式...")
            success = controller.set_led_mode(LEDMode.BREATH)
            print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
            
            if success:
                time.sleep(1)
                
                print("\n3. 测试呼吸周期设置...")
                success = controller.set_breath_period(BreathPeriod.PERIOD_3S)
                print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
                
                if success:
                    time.sleep(1)
                    
                    print("\n4. 测试关机成功消息...")
                    success = controller.send_shutdown_success()
                    print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
        
        controller.disconnect()
        print("串口已断开")
        return True
        
    else:
        print("❌ 串口直接连接失败")
        return False


def test_with_manager():
    """测试使用串口管理器的通信"""
    print("\n=== 测试串口管理器通信 ===")
    
    try:
        # 首先检查串口管理器是否运行
        from serial_manager import get_serial_manager
        
        manager = get_serial_manager()
        
        # 如果管理器未运行，启动它
        if not manager.is_running:
            print("启动串口管理器...")
            if not manager.start():
                print("❌ 无法启动串口管理器")
                return False
        
        # 创建控制器，使用串口管理器
        controller = N100PowerController(
            port='/dev/ttyS4',
            use_manager=True,
            max_retries=3,
            timeout=2.0
        )
        
        print("尝试通过串口管理器连接...")
        if controller.connect():
            print("✅ 串口管理器连接成功")
            
            # 测试LED控制
            print("\n1. 测试LED正常模式...")
            success = controller.set_led_mode(LEDMode.NORMAL)
            print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
            
            if success:
                time.sleep(1)
                
                print("\n2. 测试LED呼吸模式...")
                success = controller.set_led_mode(LEDMode.BREATH)
                print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
            
            controller.disconnect()
            print("串口管理器连接已断开")
            return success
            
        else:
            print("❌ 串口管理器连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 串口管理器测试异常: {e}")
        return False


def check_serial_device():
    """检查串口设备状态"""
    print("=== 检查串口设备状态 ===")
    
    import stat
    
    device = '/dev/ttyS4'
    
    if os.path.exists(device):
        print(f"✅ 设备 {device} 存在")
        
        # 检查设备权限
        st = os.stat(device)
        mode = stat.filemode(st.st_mode)
        print(f"   权限: {mode}")
        
        # 检查是否可读写
        if os.access(device, os.R_OK | os.W_OK):
            print("✅ 设备可读写")
        else:
            print("❌ 设备权限不足")
            print("   请运行: sudo chmod 666 /dev/ttyS4")
        
        return True
    else:
        print(f"❌ 设备 {device} 不存在")
        return False


def check_processes():
    """检查是否有其他进程占用串口"""
    print("\n=== 检查串口占用情况 ===")
    
    try:
        import subprocess
        result = subprocess.run(['lsof', '/dev/ttyS4'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("⚠️ 串口被以下进程占用:")
            print(result.stdout)
            return False
        else:
            print("✅ 串口未被其他进程占用")
            return True
            
    except Exception as e:
        print(f"无法检查进程占用: {e}")
        return True


def main():
    """主函数"""
    print("N100真实串口通信测试")
    print("=" * 40)
    
    # 1. 检查串口设备
    device_ok = check_serial_device()
    if not device_ok:
        print("\n❌ 串口设备检查失败，请检查硬件连接")
        return 1
    
    # 2. 检查进程占用
    process_ok = check_processes()
    if not process_ok:
        print("\n⚠️ 串口被其他进程占用，可能影响测试")
    
    # 3. 测试直接通信
    direct_ok = test_direct_communication()
    
    # 4. 测试串口管理器通信
    manager_ok = test_with_manager()
    
    # 5. 显示结果
    print(f"\n=== 测试结果总结 ===")
    print(f"串口设备: {'✅ 正常' if device_ok else '❌ 异常'}")
    print(f"进程占用: {'✅ 正常' if process_ok else '⚠️ 有占用'}")
    print(f"直接通信: {'✅ 正常' if direct_ok else '❌ 异常'}")
    print(f"串口管理器: {'✅ 正常' if manager_ok else '❌ 异常'}")
    
    if direct_ok:
        print("\n✅ 建议: 直接通信正常，可以使用不带串口管理器的模式")
        print("   使用方法: python3 power_ctrl_cli.py --no-manager led normal")
    elif manager_ok:
        print("\n✅ 建议: 串口管理器通信正常，可以使用串口管理器模式")
    else:
        print("\n❌ 建议: 检查硬件连接和电源板状态")
    
    return 0 if (direct_ok or manager_ok) else 1


if __name__ == "__main__":
    sys.exit(main())
