# N100电源控制系统快速启动指南

## 当前状态分析

根据您的测试结果，系统状态如下：
- ✅ 串口代理服务正在运行
- ❌ 关机守护进程未运行
- ✅ 串口设备可访问
- ✅ 代理Socket正常

## 问题解决步骤

### 1. 启动关机守护进程

```bash
# 方法1: 使用systemctl启动（推荐）
sudo systemctl start n100-shutdown-proxy

# 方法2: 手动启动（用于调试）
python3 start_shutdown_daemon.py
```

### 2. 验证服务状态

```bash
# 检查所有服务状态
systemctl status n100-serial-proxy n100-shutdown-proxy

# 或使用我们的验证脚本
./verify_shutdown_fix.sh
```

### 3. 运行专项测试

```bash
# 测试关机守护进程功能
python3 test_shutdown_daemon.py

# 测试完整关机流程
python3 test_shutdown_flow.py
```

## 理解测试结果

### 关于ACK应答的说明

**重要**: 在代理架构中，ACK应答的流程如下：

```
真实场景:
外部设备 → 串口 → 串口代理 → 立即发送ACK → 串口 → 外部设备
                     ↓
                转发给关机守护进程

测试场景:
测试客户端 → 代理Socket → 串口代理 → 串口设备
                                ↓
                           ACK从串口返回（测试客户端收不到）
```

**因此，测试中"未收到ACK应答"是正常的**，因为：
1. ACK是从串口发送给外部设备的
2. 测试客户端通过Socket连接，收不到串口的ACK
3. 这不影响实际功能

### 关于关机成功消息

关机成功消息的流程：
```
关机守护进程 → 代理Socket → 串口代理 → 串口 → 外部设备
                    ↓
               也会转发给其他客户端（包括测试客户端）
```

如果收到关机成功消息，说明关机流程正常工作。

## 手动测试真实场景

### 安全测试（不会实际关机）

```bash
# 测试LED命令（安全）
cd src
python3 power_ctrl_cli.py --use-proxy led normal
python3 power_ctrl_cli.py --use-proxy led breath
python3 power_ctrl_cli.py --use-proxy breath 3
```

### 关机流程测试（谨慎使用）

```bash
# 方法1: 通过CLI工具发送关机请求（会触发实际关机！）
cd src
python3 power_ctrl_cli.py --use-proxy shutdown

# 方法2: 直接向串口发送关机请求（会触发实际关机！）
echo -ne '\xAA\x01\x13\xED\x55' | sudo tee /dev/ttyS4
```

**⚠️ 警告**: 上述命令会触发实际的系统关机！

## 日志监控

### 实时监控日志

```bash
# 监控所有相关服务日志
sudo journalctl -u n100-serial-proxy -u n100-shutdown-proxy -f

# 只监控串口代理
sudo journalctl -u n100-serial-proxy -f

# 只监控关机守护进程
sudo journalctl -u n100-shutdown-proxy -f
```

### 查看历史日志

```bash
# 查看最近的日志
sudo journalctl -u n100-serial-proxy -n 20
sudo journalctl -u n100-shutdown-proxy -n 20

# 查看特定时间的日志
sudo journalctl -u n100-serial-proxy --since "10 minutes ago"
```

## 故障排除

### 1. 关机守护进程启动失败

```bash
# 检查详细错误
sudo journalctl -u n100-shutdown-proxy -n 20

# 手动启动查看错误
python3 start_shutdown_daemon.py
```

### 2. 串口权限问题

```bash
# 检查串口权限
ls -l /dev/ttyS4

# 修复权限
sudo chmod 666 /dev/ttyS4
sudo usermod -a -G dialout $USER
```

### 3. 代理Socket问题

```bash
# 检查Socket文件
ls -l /tmp/n100_serial_proxy.sock

# 重启代理服务
sudo systemctl restart n100-serial-proxy
```

## 验证修复效果

### 期望的正常流程

1. **启动服务**
   ```bash
   sudo systemctl start n100-serial-proxy n100-shutdown-proxy
   ```

2. **验证状态**
   ```bash
   ./verify_shutdown_fix.sh
   ```
   应该显示：
   - ✅ n100-serial-proxy: 运行中
   - ✅ n100-shutdown-proxy: 运行中

3. **测试功能**
   ```bash
   python3 test_shutdown_daemon.py
   ```
   应该显示关机守护进程能正确接收消息

### 成功标志

- 所有服务正常运行
- 测试脚本能发送消息
- 日志显示正常的消息处理
- LED控制命令正常工作

## 下一步

一旦所有服务正常运行，您可以：

1. **进行安全测试**: 使用LED控制命令验证通信
2. **监控日志**: 观察系统运行状态
3. **生产部署**: 在确认功能正常后部署到生产环境

## 联系支持

如果问题仍然存在，请提供：
1. 服务状态: `systemctl status n100-serial-proxy n100-shutdown-proxy`
2. 错误日志: `sudo journalctl -u n100-shutdown-proxy -n 50`
3. 测试结果: `python3 test_shutdown_daemon.py` 的完整输出

---

**提示**: 修复后的系统应该能正确处理关机请求，关键是确保关机守护进程正在运行。
