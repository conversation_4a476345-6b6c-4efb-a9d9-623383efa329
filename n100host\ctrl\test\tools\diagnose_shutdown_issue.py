#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
关机问题诊断工具
诊断为什么收到关机请求但不执行关机脚本
"""

import os
import sys
import subprocess
import time

def check_shutdown_service():
    """检查关机服务状态"""
    print("=== 检查关机服务状态 ===")
    
    try:
        # 检查服务是否存在
        result = subprocess.run(['systemctl', 'cat', 'n100-shutdown'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ n100-shutdown.service 服务不存在")
            print("请先安装服务:")
            print("sudo cp services/n100-shutdown.service /etc/systemd/system/")
            print("sudo systemctl daemon-reload")
            return False
        else:
            print("✅ n100-shutdown.service 服务已安装")
        
        # 检查服务状态
        result = subprocess.run(['systemctl', 'is-active', 'n100-shutdown'], 
                              capture_output=True, text=True)
        status = result.stdout.strip()
        
        if status == 'active':
            print("✅ n100-shutdown 服务正在运行")
        else:
            print(f"❌ n100-shutdown 服务状态: {status}")
            print("启动服务:")
            print("sudo systemctl start n100-shutdown")
            return False
        
        # 检查服务是否启用
        result = subprocess.run(['systemctl', 'is-enabled', 'n100-shutdown'], 
                              capture_output=True, text=True)
        enabled = result.stdout.strip()
        
        if enabled == 'enabled':
            print("✅ n100-shutdown 服务已启用")
        else:
            print(f"⚠️ n100-shutdown 服务未启用: {enabled}")
            print("启用服务:")
            print("sudo systemctl enable n100-shutdown")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查服务状态失败: {e}")
        return False

def check_shutdown_daemon_process():
    """检查关机守护进程"""
    print("\n=== 检查关机守护进程 ===")
    
    try:
        # 查找关机守护进程
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        daemon_found = False
        for line in lines:
            if 'n100_shutdown_daemon' in line and 'grep' not in line:
                print(f"✅ 找到关机守护进程: {line.strip()}")
                daemon_found = True
        
        if not daemon_found:
            print("❌ 未找到关机守护进程")
            print("可能原因:")
            print("1. 服务未启动")
            print("2. 守护进程启动失败")
            print("3. 守护进程崩溃")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查守护进程失败: {e}")
        return False

def check_serial_port_access():
    """检查串口访问"""
    print("\n=== 检查串口访问 ===")
    
    port = '/dev/ttyS4'
    
    # 检查串口设备
    if not os.path.exists(port):
        print(f"❌ 串口设备 {port} 不存在")
        return False
    else:
        print(f"✅ 串口设备 {port} 存在")
    
    # 检查权限
    if os.access(port, os.R_OK | os.W_OK):
        print(f"✅ 串口设备权限正常")
    else:
        print(f"❌ 串口设备权限不足")
        print(f"设置权限: sudo chmod 666 {port}")
        return False
    
    # 检查串口占用
    try:
        result = subprocess.run(['lsof', port], capture_output=True, text=True)
        if result.returncode == 0:
            print("⚠️ 串口被以下进程占用:")
            print(result.stdout)
            print("可能存在冲突")
        else:
            print("✅ 串口未被其他进程占用")
    except:
        print("⚠️ 无法检查串口占用情况")
    
    return True

def check_service_logs():
    """检查服务日志"""
    print("\n=== 检查服务日志 ===")
    
    try:
        # 获取最近的日志
        result = subprocess.run(['journalctl', '-u', 'n100-shutdown', '-n', '20', '--no-pager'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("最近20条日志:")
            print(result.stdout)
            
            # 分析日志内容
            if "串口打开成功" in result.stdout:
                print("✅ 串口打开成功")
            else:
                print("❌ 串口打开可能失败")
            
            if "开始监听关机请求" in result.stdout:
                print("✅ 开始监听关机请求")
            else:
                print("❌ 可能未开始监听")
            
            if "收到关机请求" in result.stdout:
                print("✅ 收到过关机请求")
            else:
                print("⚠️ 日志中未发现关机请求记录")
            
            if "执行关机命令" in result.stdout:
                print("✅ 执行过关机命令")
            else:
                print("❌ 未执行关机命令")
        else:
            print("❌ 无法获取服务日志")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查日志失败: {e}")
        return False

def test_manual_shutdown_request():
    """测试手动关机请求"""
    print("\n=== 测试手动关机请求 ===")
    
    try:
        import serial
        
        # 发送关机请求
        ser = serial.Serial('/dev/ttyS4', 115200, timeout=2)
        shutdown_request = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
        
        print(f"发送关机请求: {shutdown_request.hex(' ').upper()}")
        ser.write(shutdown_request)
        ser.flush()
        
        # 等待应答
        time.sleep(1)
        if ser.in_waiting > 0:
            response = ser.read(ser.in_waiting)
            print(f"收到应答: {response.hex(' ').upper()}")
            
            # 检查是否为正确的ACK
            expected_ack = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
            if response == expected_ack:
                print("✅ 收到正确的ACK应答")
            else:
                print("⚠️ 应答格式不正确")
        else:
            print("❌ 未收到应答")
        
        ser.close()
        
        # 等待一段时间，检查是否执行了关机
        print("等待5秒，检查是否执行关机...")
        time.sleep(5)
        
        # 检查最新日志
        result = subprocess.run(['journalctl', '-u', 'n100-shutdown', '-n', '5', '--no-pager'], 
                              capture_output=True, text=True)
        if "执行关机命令" in result.stdout:
            print("✅ 关机命令已执行")
        else:
            print("❌ 关机命令未执行")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n=== 解决方案建议 ===")
    
    print("1. 确保关机服务正确安装和启动:")
    print("   sudo cp services/n100-shutdown.service /etc/systemd/system/")
    print("   sudo systemctl daemon-reload")
    print("   sudo systemctl enable n100-shutdown")
    print("   sudo systemctl start n100-shutdown")
    
    print("\n2. 检查服务状态和日志:")
    print("   systemctl status n100-shutdown")
    print("   journalctl -u n100-shutdown -f")
    
    print("\n3. 如果服务启动失败，检查:")
    print("   - 串口权限: sudo chmod 666 /dev/ttyS4")
    print("   - 串口冲突: sudo lsof /dev/ttyS4")
    print("   - Python路径: 确保脚本路径正确")
    
    print("\n4. 手动测试关机守护进程:")
    print("   sudo python3 src/n100_shutdown_daemon.py")
    
    print("\n5. 如果仍有问题，重启服务:")
    print("   sudo systemctl restart n100-shutdown")

def main():
    """主函数"""
    print("N100关机问题诊断工具")
    print("=" * 50)
    
    # 检查是否为root用户
    if os.geteuid() != 0:
        print("⚠️ 建议使用sudo运行此脚本以获得完整诊断信息")
        print("sudo python3 test/tools/diagnose_shutdown_issue.py")
        print()
    
    results = {}
    
    # 1. 检查关机服务
    results['service'] = check_shutdown_service()
    
    # 2. 检查守护进程
    results['daemon'] = check_shutdown_daemon_process()
    
    # 3. 检查串口访问
    results['serial'] = check_serial_port_access()
    
    # 4. 检查服务日志
    results['logs'] = check_service_logs()
    
    # 5. 测试手动关机请求
    results['test'] = test_manual_shutdown_request()
    
    # 显示总结
    print("\n=== 诊断结果总结 ===")
    for check, result in results.items():
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{check}: {status}")
    
    # 提供解决方案
    if not all(results.values()):
        provide_solutions()
    else:
        print("\n✅ 所有检查都通过，关机功能应该正常工作")
    
    return 0 if all(results.values()) else 1

if __name__ == "__main__":
    sys.exit(main())
