#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复关机冲突问题
解决串口管理器和关机守护进程的冲突
"""

import os
import sys
import subprocess
import time

def stop_conflicting_services():
    """停止冲突的服务"""
    print("=== 停止冲突的服务 ===")
    
    services = ['n100-serial-manager', 'n100-shutdown']
    
    for service in services:
        try:
            print(f"停止 {service}...")
            result = subprocess.run(['systemctl', 'stop', service], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {service} 已停止")
            else:
                print(f"⚠️ {service} 停止失败或未运行")
        except Exception as e:
            print(f"❌ 停止 {service} 异常: {e}")
    
    # 等待服务完全停止
    time.sleep(2)

def check_serial_processes():
    """检查串口进程"""
    print("\n=== 检查串口进程 ===")
    
    try:
        result = subprocess.run(['lsof', '/dev/ttyS4'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("⚠️ 串口仍被占用:")
            print(result.stdout)
            
            # 提取PID并终止进程
            lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            for line in lines:
                parts = line.split()
                if len(parts) >= 2:
                    pid = parts[1]
                    try:
                        print(f"终止进程 {pid}...")
                        subprocess.run(['kill', '-TERM', pid], check=True)
                    except:
                        try:
                            subprocess.run(['kill', '-KILL', pid], check=True)
                        except:
                            pass
        else:
            print("✅ 串口未被占用")
    
    except Exception as e:
        print(f"检查串口进程失败: {e}")

def start_shutdown_service_only():
    """只启动关机服务"""
    print("\n=== 启动关机服务 ===")
    
    try:
        print("启动 n100-shutdown...")
        result = subprocess.run(['systemctl', 'start', 'n100-shutdown'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ n100-shutdown 启动成功")
        else:
            print(f"❌ n100-shutdown 启动失败: {result.stderr}")
            return False
        
        # 等待服务启动
        time.sleep(2)
        
        # 检查服务状态
        result = subprocess.run(['systemctl', 'is-active', 'n100-shutdown'], 
                              capture_output=True, text=True)
        status = result.stdout.strip()
        
        if status == 'active':
            print("✅ n100-shutdown 服务运行正常")
            return True
        else:
            print(f"❌ n100-shutdown 服务状态异常: {status}")
            return False
    
    except Exception as e:
        print(f"❌ 启动关机服务失败: {e}")
        return False

def test_shutdown_request():
    """测试关机请求"""
    print("\n=== 测试关机请求 ===")
    
    try:
        # 发送关机请求
        print("发送关机请求...")
        with open('/dev/ttyS4', 'wb') as f:
            shutdown_request = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
            f.write(shutdown_request)
            f.flush()
        
        print(f"已发送: {shutdown_request.hex(' ').upper()}")
        
        # 等待处理
        time.sleep(3)
        
        # 检查日志
        result = subprocess.run(['journalctl', '-u', 'n100-shutdown', '-n', '5', '--no-pager'], 
                              capture_output=True, text=True)
        
        if "收到电源板关机请求" in result.stdout:
            print("✅ 关机请求已被正确处理")
            return True
        elif "收到消息: 命令=0x13" in result.stdout:
            print("✅ 收到关机请求消息")
            return True
        else:
            print("❌ 关机请求未被处理")
            print("最新日志:")
            print(result.stdout)
            return False
    
    except Exception as e:
        print(f"❌ 测试关机请求失败: {e}")
        return False

def create_direct_shutdown_service():
    """创建直接访问串口的关机服务"""
    print("\n=== 创建直接关机服务 ===")
    
    service_content = """[Unit]
Description=N100 Shutdown Daemon (Direct Mode)
After=multi-user.target
Wants=multi-user.target

[Service]
Type=simple
ExecStart=/usr/bin/python3 /opt/n100/ctrl/n100_shutdown_daemon.py --port /dev/ttyS4 --direct-mode
Restart=always
RestartSec=5
User=root
Group=root

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=n100-shutdown-direct

# 环境变量
Environment=PYTHONPATH=/opt/n100/ctrl

[Install]
WantedBy=multi-user.target
"""
    
    try:
        # 写入服务文件
        with open('/tmp/n100-shutdown-direct.service', 'w') as f:
            f.write(service_content)
        
        print("✅ 创建了直接模式关机服务配置")
        print("如果需要，可以使用以下命令安装:")
        print("sudo cp /tmp/n100-shutdown-direct.service /etc/systemd/system/")
        print("sudo systemctl daemon-reload")
        print("sudo systemctl enable n100-shutdown-direct")
        print("sudo systemctl start n100-shutdown-direct")
        
        return True
    
    except Exception as e:
        print(f"❌ 创建直接关机服务失败: {e}")
        return False

def main():
    """主函数"""
    print("N100关机冲突修复工具")
    print("=" * 40)
    
    # 检查权限
    if os.geteuid() != 0:
        print("❌ 需要root权限运行此脚本")
        print("请使用: sudo python3 test/tools/fix_shutdown_conflict.py")
        return 1
    
    # 1. 停止冲突的服务
    stop_conflicting_services()
    
    # 2. 检查并清理串口进程
    check_serial_processes()
    
    # 3. 只启动关机服务
    if start_shutdown_service_only():
        # 4. 测试关机请求
        if test_shutdown_request():
            print("\n✅ 关机功能修复成功！")
            print("\n建议:")
            print("1. 保持当前配置（只运行关机服务）")
            print("2. 如需LED控制，使用直接模式:")
            print("   python3 src/power_ctrl_cli.py --no-manager led normal")
            return 0
        else:
            print("\n❌ 关机功能仍有问题")
    
    # 5. 如果仍有问题，创建直接模式服务
    print("\n尝试创建直接模式关机服务...")
    create_direct_shutdown_service()
    
    print("\n=== 手动解决步骤 ===")
    print("1. 检查关机守护进程代码是否正确处理0x13命令")
    print("2. 确保串口权限正确: sudo chmod 666 /dev/ttyS4")
    print("3. 手动运行守护进程测试: sudo python3 src/n100_shutdown_daemon.py")
    print("4. 查看详细日志: journalctl -u n100-shutdown -f")
    
    return 1

if __name__ == "__main__":
    sys.exit(main())
