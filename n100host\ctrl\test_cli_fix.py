#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试CLI工具修复效果
使用模拟器测试power_ctrl_cli.py
"""

import os
import sys
import time
import threading
import subprocess

# 添加src和test目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'test'))

try:
    from memory_serial_simulator import MemorySerial, PowerBoardSimulator
    from n100_power_ctrl import N100PowerController
    from serial_manager import SerialManager
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)


def test_cli_with_simulator():
    """测试CLI工具与模拟器"""
    print("=== 测试CLI工具与模拟器 ===")
    
    # 创建内存串口对
    n100_serial = MemorySerial("N100")
    power_serial = MemorySerial("PowerBoard")
    
    # 连接串口对
    n100_serial.connect_peer(power_serial)
    
    # 创建电源板模拟器
    power_simulator = PowerBoardSimulator(power_serial)
    
    try:
        # 启动电源板模拟器
        power_simulator.start()
        time.sleep(0.5)
        print("✅ 电源板模拟器启动成功")
        
        # 创建电源控制器（使用串口管理器模式）
        controller = N100PowerController(use_manager=True, max_retries=3, timeout=2.0)
        
        # 手动设置串口管理器
        if not controller.manager:
            controller.manager = SerialManager()
        
        # 替换串口管理器的串口
        controller.manager.serial = n100_serial
        controller.manager.is_running = True
        
        # 启动读取线程
        controller.manager._read_thread = threading.Thread(
            target=controller.manager._read_loop, daemon=True
        )
        controller.manager._read_thread.start()
        
        # 注册客户端
        success = controller.manager.register_client(
            client_id=controller.client_id,
            message_callback=controller._on_message_received,
            error_callback=controller._on_error_received
        )
        
        if success:
            controller.manager_registered = True
            controller.is_connected = True
            print("✅ 控制器连接成功")
        else:
            print("❌ 控制器连接失败")
            return False
        
        # 测试命令序列
        test_commands = [
            ("设置LED正常模式", lambda: controller.set_led_normal()),
            ("设置LED呼吸模式", lambda: controller.set_led_breath()),
            ("设置呼吸周期1秒", lambda: controller.set_breath_1s()),
            ("设置呼吸周期3秒", lambda: controller.set_breath_3s()),
            ("设置呼吸周期5秒", lambda: controller.set_breath_5s()),
        ]
        
        success_count = 0
        for desc, cmd_func in test_commands:
            print(f"\n执行: {desc}")
            if cmd_func():
                success_count += 1
                print(f"✅ {desc} - 成功")
            else:
                print(f"❌ {desc} - 失败")
            
            time.sleep(0.5)
        
        print(f"\n测试完成: {success_count}/{len(test_commands)} 个命令成功")
        
        # 清理
        if controller.manager_registered:
            controller.manager.unregister_client(controller.client_id)
        controller.manager.is_running = False
        
        return success_count == len(test_commands)
        
    except Exception as e:
        print(f"测试异常: {e}")
        return False
    
    finally:
        # 清理资源
        power_simulator.stop()


def test_direct_mode():
    """测试直接模式（不使用管理器）"""
    print("\n=== 测试直接模式 ===")
    
    # 创建内存串口对
    n100_serial = MemorySerial("N100")
    power_serial = MemorySerial("PowerBoard")
    
    # 连接串口对
    n100_serial.connect_peer(power_serial)
    
    # 创建电源板模拟器
    power_simulator = PowerBoardSimulator(power_serial)
    
    try:
        # 启动电源板模拟器
        power_simulator.start()
        time.sleep(0.5)
        print("✅ 电源板模拟器启动成功")
        
        # 创建电源控制器（直接模式）
        controller = N100PowerController(use_manager=False, max_retries=3, timeout=2.0)
        
        # 替换串口
        controller.serial = n100_serial
        controller.is_connected = True
        print("✅ 控制器连接成功（直接模式）")
        
        # 测试命令序列
        test_commands = [
            ("设置LED正常模式", lambda: controller.set_led_normal()),
            ("设置LED呼吸模式", lambda: controller.set_led_breath()),
            ("设置呼吸周期3秒", lambda: controller.set_breath_3s()),
        ]
        
        success_count = 0
        for desc, cmd_func in test_commands:
            print(f"\n执行: {desc}")
            if cmd_func():
                success_count += 1
                print(f"✅ {desc} - 成功")
            else:
                print(f"❌ {desc} - 失败")
            
            time.sleep(0.5)
        
        print(f"\n测试完成: {success_count}/{len(test_commands)} 个命令成功")
        
        return success_count == len(test_commands)
        
    except Exception as e:
        print(f"测试异常: {e}")
        return False
    
    finally:
        # 清理资源
        power_simulator.stop()


def main():
    """主函数"""
    print("开始测试CLI工具修复效果...\n")
    
    # 测试1: 串口管理器模式
    test1_result = test_cli_with_simulator()
    
    # 测试2: 直接模式
    test2_result = test_direct_mode()
    
    # 总结
    print("\n" + "="*50)
    print("测试结果总结:")
    print(f"串口管理器模式: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"直接模式: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！CLI工具修复成功。")
        print("\n建议:")
        print("1. 串口管理器的ACK检测机制已修复")
        print("2. 可以使用 --no-manager 参数避免串口冲突")
        print("3. 如果有其他服务占用串口，优先使用串口管理器模式")
        return 0
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
