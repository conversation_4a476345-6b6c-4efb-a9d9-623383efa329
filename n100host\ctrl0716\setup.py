#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制系统安装配置
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "N100电源控制系统"

# 读取requirements文件
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    requirements = []
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 只包含核心依赖
                    if line.startswith('pyserial'):
                        requirements.append(line)
    return requirements

setup(
    name="n100-power-ctrl",
    version="1.0.0",
    author="N100 Team",
    author_email="<EMAIL>",
    description="N100电源控制系统",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/n100team/ctrl",
    
    # 包配置
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    
    # Python版本要求
    python_requires=">=3.6",
    
    # 依赖包
    install_requires=read_requirements(),
    
    # 可选依赖
    extras_require={
        'dev': [
            'pytest>=6.0.0',
            'pytest-cov>=2.10.0',
            'mock>=4.0.0',
            'flake8>=3.8.0',
            'black>=20.8b1',
            'isort>=5.0.0',
            'mypy>=0.800',
        ],
        'docs': [
            'sphinx>=3.0.0',
            'sphinx-rtd-theme>=0.5.0',
        ],
    },
    
    # 命令行工具
    entry_points={
        'console_scripts': [
            'n100-power-ctrl=power_ctrl_cli:main',
        ],
    },
    
    # 包含的数据文件
    package_data={
        '': ['*.md', '*.txt', '*.yml', '*.yaml'],
    },
    
    # 分类信息
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: System Administrators",
        "License :: OSI Approved :: MIT License",
        "Operating System :: POSIX :: Linux",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: System :: Hardware",
        "Topic :: System :: Networking",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    
    # 关键词
    keywords="n100 power control serial communication embedded",
    
    # 项目URL
    project_urls={
        "Bug Reports": "https://github.com/n100team/ctrl/issues",
        "Source": "https://github.com/n100team/ctrl",
        "Documentation": "https://github.com/n100team/ctrl/wiki",
    },
    
    # 许可证
    license="MIT",
    
    # 是否包含源代码
    zip_safe=False,
)
