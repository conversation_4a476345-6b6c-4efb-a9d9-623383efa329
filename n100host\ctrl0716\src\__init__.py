#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制系统核心模块

包含电源控制和关机管理的核心功能。
"""

__version__ = "1.0.0"
__author__ = "N100 Team"
__description__ = "N100电源控制系统"

# 导出主要类和枚举
from .n100_power_ctrl import (
    N100PowerController,
    LEDMode,
    BreathPeriod,
    PowerCommand
)

from .protocol import (
    ProtocolFrame,
    MessageType,
    LEDMode as ProtocolLEDMode,
    BreathPeriod as ProtocolBreathPeriod,
    calculate_checksum,
    create_frame,
    validate_frame,
    create_led_mode_frame,
    create_breath_period_frame,
    create_shutdown_success_frame,
    create_ack_frame,
    STANDARD_FRAMES
)

__all__ = [
    # 核心控制器
    'N100PowerController',
    'LEDMode',
    'BreathPeriod',
    'PowerCommand',
    
    # 协议相关
    'ProtocolFrame',
    'MessageType',
    'ProtocolLEDMode',
    'ProtocolBreathPeriod',
    'calculate_checksum',
    'create_frame',
    'validate_frame',
    'create_led_mode_frame',
    'create_breath_period_frame',
    'create_shutdown_success_frame',
    'create_ack_frame',
    'STANDARD_FRAMES'
]
