#!/bin/bash

# N100系统测试脚本
# 自动检测当前架构并执行相应测试

set -e

echo "=== N100系统测试脚本 ==="

# 检测当前架构
detect_architecture() {
    if systemctl is-active --quiet n100-serial-proxy 2>/dev/null; then
        echo "proxy"
    elif systemctl is-active --quiet n100-shutdown-direct 2>/dev/null; then
        echo "direct"
    else
        echo "none"
    fi
}

# 通用系统检查
check_system() {
    echo "=== 系统环境检查 ==="
    
    # 检查Python
    echo "1. 检查Python环境:"
    if command -v python3 &> /dev/null; then
        python_version=$(python3 --version)
        echo "  ✅ Python: $python_version"
    else
        echo "  ❌ Python3 未安装"
        return 1
    fi
    
    # 检查pyserial
    echo "2. 检查Python依赖:"
    if python3 -c "import serial" 2>/dev/null; then
        echo "  ✅ pyserial 已安装"
    else
        echo "  ❌ pyserial 未安装"
        echo "  安装命令: pip3 install pyserial"
        return 1
    fi
    
    # 检查串口设备
    echo "3. 检查串口设备:"
    if [ -e /dev/ttyS4 ]; then
        echo "  ✅ 串口设备存在: /dev/ttyS4"
        ls -l /dev/ttyS4
    else
        echo "  ❌ 串口设备不存在: /dev/ttyS4"
        echo "  请检查硬件连接"
        return 1
    fi
    
    # 检查安装目录
    echo "4. 检查安装目录:"
    if [ -d /opt/n100/ctrl ]; then
        echo "  ✅ 安装目录存在: /opt/n100/ctrl"
        echo "  文件数量: $(ls -1 /opt/n100/ctrl/*.py 2>/dev/null | wc -l)"
    else
        echo "  ❌ 安装目录不存在: /opt/n100/ctrl"
        return 1
    fi
    
    return 0
}

# 代理架构测试
test_proxy_architecture() {
    echo ""
    echo "=== 代理架构测试 ==="
    
    # 检查服务状态
    echo "1. 检查服务状态:"
    if systemctl is-active --quiet n100-serial-proxy; then
        echo "  ✅ 串口代理服务运行正常"
    else
        echo "  ❌ 串口代理服务异常"
        systemctl status n100-serial-proxy --no-pager -l
        return 1
    fi
    
    if systemctl is-active --quiet n100-shutdown-proxy; then
        echo "  ✅ 关机代理服务运行正常"
    else
        echo "  ❌ 关机代理服务异常"
        systemctl status n100-shutdown-proxy --no-pager -l
        return 1
    fi
    
    # 检查代理Socket
    echo "2. 检查代理Socket:"
    if [ -e /tmp/n100_serial_proxy.sock ]; then
        echo "  ✅ 代理Socket存在"
        ls -l /tmp/n100_serial_proxy.sock
    else
        echo "  ❌ 代理Socket不存在"
        return 1
    fi
    
    # 检查进程
    echo "3. 检查进程:"
    if pgrep -f "serial_proxy_daemon" > /dev/null; then
        echo "  ✅ 串口代理守护进程运行中"
    else
        echo "  ❌ 串口代理守护进程未运行"
        return 1
    fi
    
    if pgrep -f "n100_shutdown_daemon_proxy" > /dev/null; then
        echo "  ✅ 关机代理守护进程运行中"
    else
        echo "  ❌ 关机代理守护进程未运行"
        return 1
    fi
    
    # 测试电源控制
    echo "4. 测试电源控制:"
    cd /opt/n100/ctrl
    if timeout 10 python3 power_ctrl_cli.py --use-proxy led normal; then
        echo "  ✅ LED控制测试成功"
    else
        echo "  ❌ LED控制测试失败"
        return 1
    fi
    
    return 0
}

# 直接架构测试
test_direct_architecture() {
    echo ""
    echo "=== 直接架构测试 ==="
    
    # 检查服务状态
    echo "1. 检查服务状态:"
    if systemctl is-active --quiet n100-shutdown-direct; then
        echo "  ✅ 关机服务运行正常"
    else
        echo "  ❌ 关机服务异常"
        systemctl status n100-shutdown-direct --no-pager -l
        return 1
    fi
    
    # 检查进程
    echo "2. 检查进程:"
    if pgrep -f "n100_shutdown_daemon_direct" > /dev/null; then
        echo "  ✅ 关机守护进程运行中"
    else
        echo "  ❌ 关机守护进程未运行"
        return 1
    fi
    
    # 测试电源控制
    echo "3. 测试电源控制:"
    cd /opt/n100/ctrl
    if timeout 10 python3 power_ctrl_cli.py led normal; then
        echo "  ✅ LED控制测试成功"
    else
        echo "  ❌ LED控制测试失败"
        return 1
    fi
    
    return 0
}

# 功能测试
test_functionality() {
    echo ""
    echo "=== 功能测试 ==="
    
    cd /opt/n100/ctrl
    
    # 确定使用的参数
    if [ "$architecture" = "proxy" ]; then
        proxy_arg="--use-proxy"
    else
        proxy_arg=""
    fi
    
    echo "1. 测试LED模式控制:"
    if timeout 5 python3 power_ctrl_cli.py $proxy_arg led normal; then
        echo "  ✅ LED正常模式设置成功"
    else
        echo "  ❌ LED正常模式设置失败"
    fi
    
    sleep 1
    
    if timeout 5 python3 power_ctrl_cli.py $proxy_arg led breath; then
        echo "  ✅ LED呼吸模式设置成功"
    else
        echo "  ❌ LED呼吸模式设置失败"
    fi
    
    echo "2. 测试呼吸周期控制:"
    for period in 1 3 5; do
        if timeout 5 python3 power_ctrl_cli.py $proxy_arg breath $period; then
            echo "  ✅ ${period}秒呼吸周期设置成功"
        else
            echo "  ❌ ${period}秒呼吸周期设置失败"
        fi
        sleep 1
    done
    
    echo "3. 测试关机成功消息:"
    if timeout 5 python3 power_ctrl_cli.py $proxy_arg shutdown; then
        echo "  ✅ 关机成功消息发送成功"
    else
        echo "  ❌ 关机成功消息发送失败"
    fi
    
    return 0
}

# 日志检查
check_logs() {
    echo ""
    echo "=== 日志检查 ==="
    
    if [ "$architecture" = "proxy" ]; then
        echo "1. 串口代理日志 (最近5条):"
        journalctl -u n100-serial-proxy -n 5 --no-pager
        
        echo ""
        echo "2. 关机代理日志 (最近5条):"
        journalctl -u n100-shutdown-proxy -n 5 --no-pager
        
    elif [ "$architecture" = "direct" ]; then
        echo "1. 关机服务日志 (最近10条):"
        journalctl -u n100-shutdown-direct -n 10 --no-pager
    fi
}

# 主函数
main() {
    # 检测架构
    architecture=$(detect_architecture)
    
    echo "检测到的架构: $architecture"
    
    case $architecture in
        "proxy")
            echo "当前使用代理架构"
            ;;
        "direct")
            echo "当前使用直接架构"
            ;;
        "none")
            echo "❌ 未检测到任何N100服务运行"
            echo "请先安装系统:"
            echo "  代理架构: sudo ./install_proxy_system.sh"
            echo "  直接架构: sudo ./install_direct_system.sh"
            exit 1
            ;;
    esac
    
    # 执行测试
    test_passed=0
    
    # 系统检查
    if check_system; then
        echo "✅ 系统环境检查通过"
        ((test_passed++))
    else
        echo "❌ 系统环境检查失败"
    fi
    
    # 架构特定测试
    if [ "$architecture" = "proxy" ]; then
        if test_proxy_architecture; then
            echo "✅ 代理架构测试通过"
            ((test_passed++))
        else
            echo "❌ 代理架构测试失败"
        fi
    elif [ "$architecture" = "direct" ]; then
        if test_direct_architecture; then
            echo "✅ 直接架构测试通过"
            ((test_passed++))
        else
            echo "❌ 直接架构测试失败"
        fi
    fi
    
    # 功能测试
    if test_functionality; then
        echo "✅ 功能测试通过"
        ((test_passed++))
    else
        echo "❌ 功能测试失败"
    fi
    
    # 显示日志
    check_logs
    
    # 测试结果
    echo ""
    echo "=== 测试结果 ==="
    echo "通过的测试: $test_passed/3"
    
    if [ $test_passed -eq 3 ]; then
        echo "🎉 所有测试通过！系统运行正常。"
        exit 0
    else
        echo "⚠️  部分测试失败，请检查系统状态。"
        exit 1
    fi
}

# 运行主函数
main "$@"
