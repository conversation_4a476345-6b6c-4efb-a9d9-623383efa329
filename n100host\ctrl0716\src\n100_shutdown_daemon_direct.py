#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100关机守护进程 - 直接串口版本
直接监听串口关机请求，避免代理连接问题
"""

import os
import sys
import time
import signal
import logging
import subprocess
import threading
import serial
from pathlib import Path

class N100ShutdownDaemonDirect:
    """N100关机守护进程 - 直接串口版本"""
    
    def __init__(self, 
                 port: str = '/dev/ttyS4',
                 baudrate: int = 115200,
                 log_file: str = '/var/log/n100_shutdown_direct.log'):
        """
        初始化关机守护进程
        
        参数:
            port: 串口设备路径
            baudrate: 波特率
            log_file: 日志文件路径
        """
        self.port = port
        self.baudrate = baudrate
        self.log_file = log_file
        
        # 串口对象
        self.serial = None
        self.running = False
        
        # 接收缓冲区
        self.rx_buffer = bytearray()
        
        # 关机状态
        self.shutdown_in_progress = False
        
        # 日志配置
        self.setup_logging()
        
        # 信号处理
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
    
    def setup_logging(self):
        """设置日志"""
        # 确保日志目录存在
        log_dir = Path(self.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('N100ShutdownDaemonDirect')
    
    def start(self) -> bool:
        """启动守护进程"""
        try:
            self.logger.info("启动N100关机守护进程（直接串口版本）...")
            
            # 打开串口
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1.0
            )
            
            if not self.serial.is_open:
                self.logger.error(f"无法打开串口 {self.port}")
                return False
            
            self.logger.info(f"串口 {self.port} 打开成功")
            
            # 启动读取线程
            self.running = True
            self.read_thread = threading.Thread(target=self._read_loop, daemon=True)
            self.read_thread.start()
            
            self.logger.info("关机守护进程启动成功，开始监听关机请求...")
            return True
            
        except Exception as e:
            self.logger.error(f"启动守护进程失败: {e}")
            return False
    
    def stop(self):
        """停止守护进程"""
        self.logger.info("停止关机守护进程...")
        self.running = False
        
        if self.serial and self.serial.is_open:
            self.serial.close()
            self.logger.info("串口已关闭")
        
        self.logger.info("关机守护进程已停止")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备停止守护进程...")
        self.stop()
        sys.exit(0)
    
    def _read_loop(self):
        """串口读取循环"""
        while self.running:
            try:
                if self.serial and self.serial.in_waiting > 0:
                    data = self.serial.read(self.serial.in_waiting)
                    if data:
                        self.logger.debug(f"接收数据: {data.hex(' ').upper()}")
                        self.rx_buffer.extend(data)
                        self._parse_messages()
                
                time.sleep(0.01)
                
            except Exception as e:
                self.logger.error(f"串口读取异常: {e}")
                time.sleep(1)
    
    def _parse_messages(self):
        """解析消息"""
        # 关机请求帧: AA 01 13 ED 55
        shutdown_req = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
        
        # 在缓冲区中查找关机请求
        pos = self.rx_buffer.find(shutdown_req)
        if pos != -1:
            # 找到关机请求
            self.logger.info(f"收到电源板关机请求: {shutdown_req.hex(' ').upper()}")
            
            # 移除已处理的数据
            self.rx_buffer = self.rx_buffer[pos + len(shutdown_req):]
            
            # 处理关机请求
            self._handle_shutdown_request()
        
        # 如果缓冲区过大，清理旧数据
        if len(self.rx_buffer) > 1024:
            self.rx_buffer = self.rx_buffer[-1024:]
    
    def _handle_shutdown_request(self):
        """处理关机请求"""
        if self.shutdown_in_progress:
            self.logger.warning("关机流程已在进行中，忽略重复请求")
            return
        
        self.shutdown_in_progress = True
        
        try:
            # 立即发送ACK应答
            self._send_ack_response()
            
            # 执行关机流程
            self._execute_shutdown()
            
        except Exception as e:
            self.logger.error(f"处理关机请求异常: {e}")
            self.shutdown_in_progress = False
    
    def _send_ack_response(self):
        """发送ACK应答"""
        try:
            # 创建ACK帧
            ack_frame = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
            
            # 发送ACK
            if self.serial and self.serial.is_open:
                self.serial.write(ack_frame)
                self.serial.flush()
                self.logger.info(f"已发送ACK应答: {ack_frame.hex(' ').upper()}")
            else:
                self.logger.error("串口未打开，无法发送ACK应答")
        
        except Exception as e:
            self.logger.error(f"发送ACK应答异常: {e}")
    
    def _execute_shutdown(self):
        """执行关机流程"""
        try:
            self.logger.info("开始执行关机流程...")
            
            # 启动关机线程
            shutdown_thread = threading.Thread(target=self._shutdown_worker, daemon=True)
            shutdown_thread.start()
        
        except Exception as e:
            self.logger.error(f"执行关机流程异常: {e}")
            self.shutdown_in_progress = False
    
    def _shutdown_worker(self):
        """关机工作线程"""
        try:
            # 等待一小段时间确保ACK发送完成
            time.sleep(0.5)
            
            # 执行关机命令
            self.logger.info("执行关机命令: sudo shutdown -h now")
            
            # 启动关机成功通知线程
            notify_thread = threading.Thread(target=self._shutdown_success_notifier, daemon=True)
            notify_thread.start()
            
            # 执行系统关机
            subprocess.run(['sudo', 'shutdown', '-h', 'now'], check=True)
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"关机命令执行失败: {e}")
        except Exception as e:
            self.logger.error(f"关机工作线程异常: {e}")
    
    def _shutdown_success_notifier(self):
        """关机成功通知线程"""
        try:
            # 等待系统开始关机流程
            time.sleep(2)
            
            # 检测系统关机状态
            max_wait = 30  # 最多等待30秒
            for i in range(max_wait):
                try:
                    # 检查是否有关机进程
                    result = subprocess.run(['pgrep', 'shutdown'], 
                                          capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        self.logger.info("检测到系统关机进程，准备发送关机成功消息")
                        break
                    
                    time.sleep(1)
                
                except Exception:
                    time.sleep(1)
            
            # 等待文件系统同步
            time.sleep(3)
            
            # 发送关机成功消息
            self._send_shutdown_success()
            
        except Exception as e:
            self.logger.error(f"关机成功通知异常: {e}")
    
    def _send_shutdown_success(self):
        """发送关机成功消息"""
        try:
            # 创建关机成功帧
            shutdown_frame = bytes([0xAA, 0x01, 0x03, 0xFD, 0x55])
            
            # 发送关机成功消息
            if self.serial and self.serial.is_open:
                self.serial.write(shutdown_frame)
                self.serial.flush()
                self.logger.info(f"已发送关机成功消息: {shutdown_frame.hex(' ').upper()}")
            else:
                self.logger.error("串口未打开，无法发送关机成功消息")
        
        except Exception as e:
            self.logger.error(f"发送关机成功消息异常: {e}")
    
    def run(self):
        """运行守护进程"""
        if not self.start():
            return 1
        
        try:
            # 保持运行
            while self.running:
                time.sleep(1)
        
        except KeyboardInterrupt:
            self.logger.info("收到中断信号")
        
        finally:
            self.stop()
        
        return 0


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='N100关机守护进程（直接串口版本）')
    parser.add_argument('--port', default='/dev/ttyS4', help='串口设备路径')
    parser.add_argument('--baudrate', type=int, default=115200, help='波特率')
    parser.add_argument('--log', default='/var/log/n100_shutdown_direct.log', help='日志文件路径')
    
    args = parser.parse_args()
    
    # 创建并运行守护进程
    daemon = N100ShutdownDaemonDirect(
        port=args.port,
        baudrate=args.baudrate,
        log_file=args.log
    )
    
    return daemon.run()


if __name__ == "__main__":
    sys.exit(main())
