#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制系统通信协议模块

定义了N100主机与电源板之间的通信协议，包括：
- 消息帧格式定义
- 校验和计算
- 帧创建和验证
- 协议常量定义

消息帧格式：
| 帧头 | 长度 | 命令 | 数据 | 校验和 | 帧尾 |
| 0xAA | 1字节 | 1字节 | N字节 | 1字节 | 0x55 |

校验和计算：对命令+数据进行求和，然后取反加1
"""

from typing import Optional, Tuple, List
from dataclasses import dataclass
from enum import IntEnum


# ========================================================================
#                              协议常量
# ========================================================================

# 协议帧头和帧尾
PROTOCOL_HEADER = 0xAA        # 帧头
PROTOCOL_TAIL = 0x55          # 帧尾

# 协议长度定义
PROTOCOL_MIN_LEN = 5          # 最小协议长度(头+长度+命令+校验+尾)
PROTOCOL_MAX_LEN = 20         # 最大协议长度
PROTOCOL_DATA_MAX = 13        # 最大数据长度


# ========================================================================
#                              命令类型定义
# ========================================================================

class MessageType(IntEnum):
    """消息类型枚举"""
    # 接收的消息（自动发送ACK应答）
    LED_MODE = 0x01           # LED模式设置命令
    BREATH_PERIOD = 0x02      # 呼吸灯周期设置命令
    SHUTDOWN_SUCCESS = 0x03   # 关机成功消息
    
    # 发送的消息（等待ACK应答）
    SHUTDOWN_REQ = 0x13       # 关机请求命令
    
    # 应答消息
    ACK = 0x80               # 通用应答命令


class LEDMode(IntEnum):
    """LED模式枚举"""
    NORMAL = 0x00            # LED正常模式
    BREATH = 0x01            # LED呼吸灯模式


class BreathPeriod(IntEnum):
    """呼吸周期枚举"""
    PERIOD_1S = 1            # 1秒周期
    PERIOD_3S = 3            # 3秒周期
    PERIOD_5S = 5            # 5秒周期


# ========================================================================
#                              协议帧结构
# ========================================================================

@dataclass
class ProtocolFrame:
    """协议帧数据结构"""
    header: int = PROTOCOL_HEADER
    length: int = 0
    command: int = 0
    data: bytes = b''
    checksum: int = 0
    tail: int = PROTOCOL_TAIL
    
    def to_bytes(self) -> bytes:
        """转换为字节数组"""
        return bytes([
            self.header,
            self.length,
            self.command
        ]) + self.data + bytes([
            self.checksum,
            self.tail
        ])
    
    @classmethod
    def from_bytes(cls, data: bytes) -> Optional['ProtocolFrame']:
        """从字节数组创建协议帧"""
        if not validate_frame(data):
            return None
        
        if len(data) < PROTOCOL_MIN_LEN:
            return None
        
        header = data[0]
        length = data[1]
        command = data[2]
        frame_data = data[3:3+length-1] if length > 1 else b''
        checksum = data[3+length-1]
        tail = data[3+length]
        
        return cls(
            header=header,
            length=length,
            command=command,
            data=frame_data,
            checksum=checksum,
            tail=tail
        )


# ========================================================================
#                              协议工具函数
# ========================================================================

def calculate_checksum(data: bytes) -> int:
    """
    计算校验和
    
    参数:
        data (bytes): 需要计算校验和的数据(命令+数据)
    
    返回:
        int: 校验和，使用二进制补码方法 (~sum + 1)
    """
    checksum = sum(data) & 0xFF
    return ((~checksum) + 1) & 0xFF


def create_frame(command: int, data: bytes = b'') -> ProtocolFrame:
    """
    创建协议帧
    
    参数:
        command (int): 命令字节
        data (bytes): 数据字节
    
    返回:
        ProtocolFrame: 协议帧对象
    """
    if len(data) > PROTOCOL_DATA_MAX:
        raise ValueError(f"数据长度超过最大限制({PROTOCOL_DATA_MAX}字节)")
    
    length = len(data) + 1  # 数据长度 + 命令长度
    
    # 校验和只对命令+数据计算
    checksum_data = bytes([command]) + data
    checksum = calculate_checksum(checksum_data)
    
    return ProtocolFrame(
        length=length,
        command=command,
        data=data,
        checksum=checksum
    )


def validate_frame(frame_data: bytes) -> bool:
    """
    验证协议帧
    
    参数:
        frame_data (bytes): 帧数据
    
    返回:
        bool: 是否为有效帧
    """
    if len(frame_data) < PROTOCOL_MIN_LEN:
        return False
    
    # 检查帧头和帧尾
    if frame_data[0] != PROTOCOL_HEADER or frame_data[-1] != PROTOCOL_TAIL:
        return False
    
    # 检查长度
    length = frame_data[1]
    expected_frame_len = length + 4  # 头(1) + 长度(1) + 校验(1) + 尾(1)
    if len(frame_data) != expected_frame_len:
        return False
    
    # 检查校验和
    command = frame_data[2]
    data = frame_data[3:3+length-1] if length > 1 else b''
    expected_checksum = frame_data[3+length-1]
    
    checksum_data = bytes([command]) + data
    actual_checksum = calculate_checksum(checksum_data)
    
    return expected_checksum == actual_checksum


# ========================================================================
#                              预定义帧创建函数
# ========================================================================

def create_led_mode_frame(mode: LEDMode) -> ProtocolFrame:
    """创建LED模式设置帧"""
    return create_frame(MessageType.LED_MODE, bytes([mode]))


def create_breath_period_frame(period: BreathPeriod) -> ProtocolFrame:
    """创建呼吸周期设置帧"""
    return create_frame(MessageType.BREATH_PERIOD, bytes([period]))


def create_shutdown_success_frame() -> ProtocolFrame:
    """创建关机成功帧"""
    return create_frame(MessageType.SHUTDOWN_SUCCESS)


def create_shutdown_request_frame() -> ProtocolFrame:
    """创建关机请求帧"""
    return create_frame(MessageType.SHUTDOWN_REQ)


def create_ack_frame() -> ProtocolFrame:
    """创建ACK应答帧"""
    return create_frame(MessageType.ACK)


# ========================================================================
#                              协议解析函数
# ========================================================================

def parse_frame(frame_data: bytes) -> Tuple[bool, int, bytes]:
    """
    解析协议帧
    
    参数:
        frame_data (bytes): 帧数据
    
    返回:
        Tuple[bool, int, bytes]: (是否有效, 命令类型, 数据内容)
    """
    if not validate_frame(frame_data):
        return False, 0, b''
    
    length = frame_data[1]
    command = frame_data[2]
    data = frame_data[3:3+length-1] if length > 1 else b''
    
    return True, command, data


def get_frame_description(command: int, data: bytes = b'') -> str:
    """
    获取帧描述信息
    
    参数:
        command (int): 命令类型
        data (bytes): 数据内容
    
    返回:
        str: 帧描述
    """
    if command == MessageType.LED_MODE:
        if len(data) > 0:
            mode = "正常模式" if data[0] == LEDMode.NORMAL else "呼吸模式"
            return f"LED模式设置 - {mode}"
        return "LED模式设置"
    
    elif command == MessageType.BREATH_PERIOD:
        if len(data) > 0:
            return f"呼吸周期设置 - {data[0]}秒"
        return "呼吸周期设置"
    
    elif command == MessageType.SHUTDOWN_SUCCESS:
        return "关机成功消息"
    
    elif command == MessageType.SHUTDOWN_REQ:
        return "关机请求"
    
    elif command == MessageType.ACK:
        return "ACK应答"
    
    else:
        return f"未知命令(0x{command:02X})"


# ========================================================================
#                              预定义帧常量
# ========================================================================

# 预定义的标准帧（字节格式）
STANDARD_FRAMES = {
    'led_normal': bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55]),      # LED正常模式
    'led_breath': bytes([0xAA, 0x02, 0x01, 0x01, 0xFE, 0x55]),      # LED呼吸模式
    'breath_1s': bytes([0xAA, 0x02, 0x02, 0x01, 0xFD, 0x55]),       # 1秒呼吸周期
    'breath_3s': bytes([0xAA, 0x02, 0x02, 0x03, 0xFB, 0x55]),       # 3秒呼吸周期
    'breath_5s': bytes([0xAA, 0x02, 0x02, 0x05, 0xF9, 0x55]),       # 5秒呼吸周期
    'shutdown_success': bytes([0xAA, 0x01, 0x03, 0xFD, 0x55]),      # 关机成功
    'shutdown_request': bytes([0xAA, 0x01, 0x13, 0xED, 0x55]),      # 关机请求
    'ack': bytes([0xAA, 0x01, 0x80, 0x80, 0x55]),                   # ACK应答
}


def get_standard_frame(frame_name: str) -> Optional[bytes]:
    """
    获取标准帧
    
    参数:
        frame_name (str): 帧名称
    
    返回:
        Optional[bytes]: 帧数据，如果不存在返回None
    """
    return STANDARD_FRAMES.get(frame_name)


def list_standard_frames() -> List[str]:
    """
    列出所有标准帧名称
    
    返回:
        List[str]: 标准帧名称列表
    """
    return list(STANDARD_FRAMES.keys())
