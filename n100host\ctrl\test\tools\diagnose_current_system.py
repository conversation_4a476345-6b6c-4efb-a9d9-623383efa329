#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
当前系统状态诊断工具
检查当前运行的服务和串口状态
"""

import os
import sys
import subprocess
import time

def check_running_services():
    """检查正在运行的服务"""
    print("=== 检查正在运行的服务 ===")
    
    services = [
        'n100-shutdown',
        'n100-serial-manager', 
        'n100-serial-proxy',
        'n100-shutdown-proxy'
    ]
    
    running_services = []
    
    for service in services:
        try:
            result = subprocess.run(['systemctl', 'is-active', service], 
                                  capture_output=True, text=True)
            status = result.stdout.strip()
            
            if status == 'active':
                print(f"✅ {service}: 运行中")
                running_services.append(service)
            else:
                print(f"❌ {service}: {status}")
        except Exception as e:
            print(f"❌ {service}: 检查失败 - {e}")
    
    return running_services

def check_serial_processes():
    """检查占用串口的进程"""
    print("\n=== 检查串口占用情况 ===")
    
    try:
        result = subprocess.run(['lsof', '/dev/ttyS4'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("串口被以下进程占用:")
            lines = result.stdout.strip().split('\n')
            for line in lines:
                print(f"  {line}")
            
            # 解析进程信息
            processes = []
            for line in lines[1:]:  # 跳过标题行
                parts = line.split()
                if len(parts) >= 2:
                    processes.append({
                        'command': parts[0],
                        'pid': parts[1],
                        'user': parts[2]
                    })
            
            return processes
        else:
            print("✅ 串口未被占用")
            return []
            
    except Exception as e:
        print(f"检查串口占用失败: {e}")
        return []

def check_proxy_socket():
    """检查代理Socket"""
    print("\n=== 检查代理Socket ===")
    
    socket_paths = [
        '/tmp/n100_serial_proxy.sock',
        '/var/run/n100_serial_proxy.sock'
    ]
    
    for socket_path in socket_paths:
        if os.path.exists(socket_path):
            print(f"✅ 找到代理Socket: {socket_path}")
            # 检查权限
            stat_info = os.stat(socket_path)
            print(f"   权限: {oct(stat_info.st_mode)[-3:]}")
            return socket_path
        else:
            print(f"❌ Socket不存在: {socket_path}")
    
    return None

def test_direct_serial():
    """测试直接串口通信"""
    print("\n=== 测试直接串口通信 ===")
    
    try:
        import serial
        
        # 尝试打开串口
        ser = serial.Serial('/dev/ttyS4', 115200, timeout=2)
        print("✅ 串口打开成功")
        
        # 发送关机请求
        shutdown_request = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
        print(f"发送关机请求: {shutdown_request.hex(' ').upper()}")
        
        ser.write(shutdown_request)
        ser.flush()
        
        # 等待应答
        time.sleep(1)
        if ser.in_waiting > 0:
            response = ser.read(ser.in_waiting)
            print(f"收到应答: {response.hex(' ').upper()}")
            
            # 检查是否为ACK
            expected_ack = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
            if response == expected_ack:
                print("✅ 收到正确的ACK应答")
                return True
            else:
                print("⚠️ 应答格式不正确")
                return False
        else:
            print("❌ 未收到应答")
            return False
        
        ser.close()
        
    except Exception as e:
        print(f"❌ 直接串口测试失败: {e}")
        return False

def get_service_logs(service_name, lines=10):
    """获取服务日志"""
    try:
        result = subprocess.run(['journalctl', '-u', service_name, '-n', str(lines), '--no-pager'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            return result.stdout
        else:
            return f"无法获取 {service_name} 日志"
    except Exception as e:
        return f"获取日志异常: {e}"

def main():
    """主函数"""
    print("N100当前系统状态诊断")
    print("=" * 40)
    
    # 1. 检查服务状态
    running_services = check_running_services()
    
    # 2. 检查串口占用
    processes = check_serial_processes()
    
    # 3. 检查代理Socket
    proxy_socket = check_proxy_socket()
    
    # 4. 测试直接串口
    serial_ok = test_direct_serial()
    
    # 5. 分析当前状态
    print(f"\n=== 系统状态分析 ===")
    
    if 'n100-serial-proxy' in running_services:
        print("🔧 检测到串口代理服务运行中")
        if proxy_socket:
            print("✅ 代理架构已部署")
            print("建议使用代理模式:")
            print("  python3 src/power_ctrl_cli.py --use-proxy test")
        else:
            print("❌ 代理服务运行但Socket不存在")
    
    elif 'n100-shutdown' in running_services:
        print("🔧 检测到旧版关机服务运行中")
        if processes:
            print("⚠️ 存在串口冲突")
            print("建议:")
            print("1. 停止旧服务: sudo systemctl stop n100-shutdown n100-serial-manager")
            print("2. 安装新架构: sudo bash scripts/install_proxy_system.sh")
        else:
            print("✅ 旧架构运行正常")
    
    else:
        print("🔧 未检测到N100服务运行")
        if serial_ok:
            print("✅ 串口通信正常")
            print("建议:")
            print("1. 安装新架构: sudo bash scripts/install_proxy_system.sh")
            print("2. 或使用直接模式: python3 src/power_ctrl_cli.py --no-manager test")
        else:
            print("❌ 串口通信异常")
            print("建议:")
            print("1. 检查硬件连接")
            print("2. 检查电源板状态")
            print("3. 设置串口权限: sudo chmod 666 /dev/ttyS4")
    
    # 6. 显示相关日志
    if running_services:
        print(f"\n=== 服务日志 ===")
        for service in running_services:
            print(f"\n{service} 最新日志:")
            logs = get_service_logs(service, 5)
            print(logs)
    
    # 7. 提供解决方案
    print(f"\n=== 解决方案 ===")
    
    if not running_services and not serial_ok:
        print("🚨 系统未正常工作")
        print("1. 检查硬件: 确认电源板连接和上电")
        print("2. 检查权限: sudo chmod 666 /dev/ttyS4")
        print("3. 手动测试: echo -e '\\xAA\\x01\\x13\\xED\\x55' | sudo tee /dev/ttyS4")
    
    elif not running_services and serial_ok:
        print("🔧 硬件正常，需要安装服务")
        print("1. 安装新架构: sudo bash scripts/install_proxy_system.sh")
        print("2. 或使用直接模式: python3 src/power_ctrl_cli.py --no-manager shutdown")
    
    elif 'n100-serial-proxy' in running_services:
        print("🎯 使用新架构")
        print("1. 测试代理: python3 src/power_ctrl_cli.py --use-proxy test")
        print("2. 发送关机: python3 src/power_ctrl_cli.py --use-proxy shutdown")
    
    else:
        print("🔄 迁移到新架构")
        print("1. 停止旧服务: sudo systemctl stop n100-shutdown n100-serial-manager")
        print("2. 安装新架构: sudo bash scripts/install_proxy_system.sh")
        print("3. 测试新架构: n100-test-proxy")

if __name__ == "__main__":
    main()
