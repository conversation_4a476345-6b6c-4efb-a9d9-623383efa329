# N100关机流程详细说明

## 概述

本文档详细说明了N100电源控制系统中关机流程的实现，包括代理架构和直接架构下的处理方式。

## 期望的关机流程

### 标准关机流程

1. **外部设备发送关机请求**
   - 电源板通过串口发送关机请求帧：`AA 01 13 ED 55`
   - 帧格式：帧头(0xAA) + 长度(0x01) + 命令(0x13) + 校验和(0xED) + 帧尾(0x55)

2. **N100接收关机请求**
   - N100通过串口接收关机请求
   - 验证帧格式和校验和

3. **N100发送ACK应答**
   - 立即发送ACK应答帧：`AA 01 80 80 55`
   - 确认已收到关机请求

4. **N100执行关机脚本**
   - 执行系统关机命令
   - 等待系统开始关机流程

5. **N100发送关机成功通知**
   - 在系统关机前发送关机成功帧：`AA 01 03 FD 55`
   - 通知电源板关机流程已启动

## 代理架构实现

### 架构组件

```
外部设备 → 串口(/dev/ttyS4) → 串口代理守护进程 → Unix Socket → 关机守护进程代理版
                                    ↓
                              自动ACK应答
                                    ↓
                              转发给关机守护进程
                                    ↓
                              执行关机流程
                                    ↓
                              发送关机成功消息
```

### 详细流程

#### 1. 串口代理守护进程 (`serial_proxy_daemon.py`)

**职责**：
- 独占串口 `/dev/ttyS4`
- 接收所有串口数据
- 自动发送ACK应答
- 转发消息给注册的客户端

**关机请求处理**：
```python
# 接收到关机请求帧
if command == MessageType.SHUTDOWN_REQ:
    # 1. 立即发送ACK应答
    self._send_auto_ack()
    
    # 2. 转发给所有客户端（包括关机守护进程）
    self._forward_to_clients(message)
```

#### 2. 关机守护进程代理版 (`n100_shutdown_daemon_proxy.py`)

**职责**：
- 通过Unix Socket连接到串口代理
- 监听关机请求消息
- 执行关机流程
- 发送关机成功通知

**关机请求处理**：
```python
def _on_message(self, message: ProxyMessage):
    if message.command == MessageType.SHUTDOWN_REQ:
        # 1. 记录收到关机请求
        self.logger.info("收到电源板关机请求")
        
        # 2. 代理已发送ACK，直接执行关机
        self._execute_shutdown()

def _shutdown_worker(self):
    # 1. 等待ACK发送完成
    time.sleep(1.0)
    
    # 2. 发送关机成功通知
    self._send_shutdown_success()
    
    # 3. 等待消息发送完成
    time.sleep(0.5)
    
    # 4. 执行系统关机
    subprocess.run(['sudo', 'shutdown', '-h', 'now'])
```

### 消息过滤

关机守护进程设置消息过滤器，只接收关机请求：
```python
self.proxy_client.set_message_filter([MessageType.SHUTDOWN_REQ])
```

## 直接架构实现

### 架构组件

```
外部设备 → 串口(/dev/ttyS4) → 直接关机守护进程
                                    ↓
                              接收关机请求
                                    ↓
                              发送ACK应答
                                    ↓
                              执行关机流程
                                    ↓
                              发送关机成功消息
```

### 详细流程

直接架构中，关机守护进程直接访问串口，处理完整的关机流程。

## 协议帧定义

### 关机请求帧 (0x13)
```
AA 01 13 ED 55
│  │  │  │  │
│  │  │  │  └─ 帧尾 (0x55)
│  │  │  └──── 校验和 (0xED)
│  │  └─────── 命令 (0x13 - 关机请求)
│  └────────── 长度 (0x01 - 只有命令字节)
└─────────────── 帧头 (0xAA)
```

### ACK应答帧 (0x80)
```
AA 01 80 80 55
│  │  │  │  │
│  │  │  │  └─ 帧尾 (0x55)
│  │  │  └──── 校验和 (0x80)
│  │  └─────── 命令 (0x80 - ACK应答)
│  └────────── 长度 (0x01)
└─────────────── 帧头 (0xAA)
```

### 关机成功帧 (0x03)
```
AA 01 03 FD 55
│  │  │  │  │
│  │  │  │  └─ 帧尾 (0x55)
│  │  │  └──── 校验和 (0xFD)
│  │  └─────── 命令 (0x03 - 关机成功)
│  └────────── 长度 (0x01)
└─────────────── 帧头 (0xAA)
```

## 时序图

### 代理架构关机时序

```
外部设备          串口代理守护进程      关机守护进程代理版      系统
    │                    │                    │               │
    │─── 关机请求 ────────→│                    │               │
    │    (AA 01 13 ED 55) │                    │               │
    │                    │                    │               │
    │←─── ACK应答 ─────────│                    │               │
    │    (AA 01 80 80 55) │                    │               │
    │                    │                    │               │
    │                    │─── 转发关机请求 ────→│               │
    │                    │                    │               │
    │                    │                    │─── 关机命令 ───→│
    │                    │                    │               │
    │←─── 关机成功 ────────│←─── 关机成功 ───────│               │
    │    (AA 01 03 FD 55) │                    │               │
    │                    │                    │               │
    │                    │                    │               │←─ 系统关机
```

## 修复的问题

### 原问题
在代理架构中，关机请求无响应，主要原因：
1. 串口代理对关机请求不发送自动ACK
2. 关机守护进程重复发送ACK
3. 时序问题导致消息丢失

### 修复方案
1. **统一ACK处理**：串口代理对所有命令（除ACK外）自动发送ACK
2. **简化关机流程**：关机守护进程不再重复发送ACK
3. **优化时序**：在关机前先发送关机成功消息
4. **消息过滤**：关机守护进程只接收关机请求

### 修改的文件

#### `serial_proxy_daemon.py`
```python
# 修改前：不对关机请求发送ACK
if command != MessageType.ACK and command != MessageType.SHUTDOWN_REQ:
    self._send_auto_ack()

# 修改后：对所有命令发送ACK
if command != MessageType.ACK:
    self._send_auto_ack()
    self.logger.info(f"已对命令 0x{command:02X} 发送自动ACK应答")
```

#### `n100_shutdown_daemon_proxy.py`
```python
# 修改前：重复发送ACK
self._send_ack_response()
self._execute_shutdown()

# 修改后：直接执行关机
self.logger.info("代理已发送ACK应答，开始执行关机流程")
self._execute_shutdown()
```

## 测试验证

### 测试脚本
使用 `test_shutdown_flow.py` 脚本验证关机流程：

```bash
python test_shutdown_flow.py
```

### 测试步骤
1. 检查服务状态
2. 发送关机请求
3. 验证ACK应答
4. 验证关机成功消息

### 预期结果
```
✅ n100-serial-proxy: 运行中
✅ n100-shutdown-proxy: 运行中
✅ 关机请求发送成功
✅ 收到ACK应答
✅ 收到关机成功消息
🎉 所有测试通过！关机流程工作正常。
```

## 故障排除

### 常见问题

1. **未收到ACK应答**
   - 检查串口代理服务是否运行
   - 检查串口设备权限
   - 查看代理服务日志

2. **未收到关机成功消息**
   - 检查关机守护进程是否运行
   - 检查关机守护进程日志
   - 确认关机命令权限

3. **服务连接失败**
   - 检查Unix Socket文件权限
   - 重启代理服务
   - 检查防火墙设置

### 日志查看

```bash
# 串口代理日志
journalctl -u n100-serial-proxy -f

# 关机守护进程日志
journalctl -u n100-shutdown-proxy -f

# 系统日志
dmesg | tail -20
```

## 最佳实践

1. **使用代理架构**：推荐在生产环境使用代理架构
2. **监控服务状态**：定期检查服务运行状态
3. **测试关机流程**：定期运行测试脚本验证功能
4. **备份配置**：定期备份服务配置和日志

---

**版本**: 1.0.0  
**最后更新**: 2025-07-16  
**维护者**: N100 Team
