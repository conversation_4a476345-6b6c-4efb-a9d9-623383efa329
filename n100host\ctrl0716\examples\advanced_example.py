#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制器高级使用示例
演示高级功能，包括自定义命令、协议帧操作、错误处理等
"""

import time
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))

from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod, PowerCommand
from protocol import (
    create_frame, validate_frame, calculate_checksum,
    create_led_mode_frame, create_breath_period_frame,
    STANDARD_FRAMES, get_frame_description
)


def advanced_callback_example():
    """高级回调使用示例"""
    print("=== 高级回调使用示例 ===")
    
    # 统计变量
    ack_count = 0
    error_count = 0
    
    def detailed_ack_callback(command):
        nonlocal ack_count
        ack_count += 1
        description = get_frame_description(command)
        print(f"[ACK #{ack_count}] 命令: 0x{command:02X} ({description})")
    
    def detailed_error_callback(error_msg):
        nonlocal error_count
        error_count += 1
        print(f"[ERROR #{error_count}] {error_msg}")
    
    controller = N100PowerController()
    controller.set_ack_callback(detailed_ack_callback)
    controller.set_error_callback(detailed_error_callback)
    
    if not controller.connect():
        print("❌ 无法连接串口")
        return False
    
    try:
        print("\n执行一系列命令...")
        
        # 执行多个命令
        commands = [
            ("设置LED正常模式", controller.set_led_normal),
            ("设置LED呼吸模式", controller.set_led_breath),
            ("设置1秒呼吸周期", controller.set_breath_1s),
            ("设置3秒呼吸周期", controller.set_breath_3s),
            ("发送关机成功", controller.send_shutdown_success),
        ]
        
        for desc, cmd_func in commands:
            print(f"\n执行: {desc}")
            if cmd_func():
                print(f"✅ {desc} - 成功")
            else:
                print(f"❌ {desc} - 失败")
            time.sleep(1)
        
        print(f"\n统计信息:")
        print(f"  ACK应答次数: {ack_count}")
        print(f"  错误次数: {error_count}")
        
        return True
        
    finally:
        controller.disconnect()


def custom_command_example():
    """自定义命令示例"""
    print("\n=== 自定义命令示例 ===")
    
    controller = N100PowerController()
    
    if not controller.connect():
        print("❌ 无法连接串口")
        return False
    
    try:
        print("\n发送自定义命令...")
        
        # 自定义命令示例
        custom_commands = [
            (0x01, b'\x00', "LED正常模式（自定义）"),
            (0x01, b'\x01', "LED呼吸模式（自定义）"),
            (0x02, b'\x03', "3秒呼吸周期（自定义）"),
            (0x03, b'', "关机成功（自定义）"),
        ]
        
        for command, data, description in custom_commands:
            print(f"\n发送自定义命令: {description}")
            print(f"  命令: 0x{command:02X}, 数据: {data.hex(' ').upper() if data else '无'}")
            
            if controller.send_custom_command(command, data):
                print(f"  ✅ {description} - 成功")
            else:
                print(f"  ❌ {description} - 失败")
            time.sleep(1)
        
        return True
        
    finally:
        controller.disconnect()


def protocol_frame_example():
    """协议帧操作示例"""
    print("\n=== 协议帧操作示例 ===")
    
    print("\n1. 创建协议帧:")
    
    # 创建各种协议帧
    frames = [
        create_led_mode_frame(LEDMode.NORMAL),
        create_led_mode_frame(LEDMode.BREATH),
        create_breath_period_frame(BreathPeriod.PERIOD_3S),
        create_frame(PowerCommand.SHUTDOWN),
    ]
    
    frame_names = ["LED正常模式", "LED呼吸模式", "3秒呼吸周期", "关机成功"]
    
    for frame, name in zip(frames, frame_names):
        frame_bytes = frame.to_bytes()
        print(f"  {name}: {frame_bytes.hex(' ').upper()}")
        
        # 验证帧
        if validate_frame(frame_bytes):
            print(f"    ✅ 帧验证通过")
        else:
            print(f"    ❌ 帧验证失败")
    
    print("\n2. 标准帧对比:")
    
    # 对比标准帧
    standard_comparisons = [
        ("led_normal", create_led_mode_frame(LEDMode.NORMAL)),
        ("led_breath", create_led_mode_frame(LEDMode.BREATH)),
        ("breath_3s", create_breath_period_frame(BreathPeriod.PERIOD_3S)),
        ("shutdown_success", create_frame(PowerCommand.SHUTDOWN)),
    ]
    
    for std_name, created_frame in standard_comparisons:
        std_frame = STANDARD_FRAMES.get(std_name)
        created_bytes = created_frame.to_bytes()
        
        print(f"  {std_name}:")
        print(f"    标准帧: {std_frame.hex(' ').upper()}")
        print(f"    创建帧: {created_bytes.hex(' ').upper()}")
        
        if std_frame == created_bytes:
            print(f"    ✅ 帧匹配")
        else:
            print(f"    ❌ 帧不匹配")
    
    print("\n3. 校验和计算示例:")
    
    # 校验和计算示例
    test_data = [
        (bytes([0x01, 0x00]), "LED正常模式"),
        (bytes([0x01, 0x01]), "LED呼吸模式"),
        (bytes([0x02, 0x03]), "3秒呼吸周期"),
        (bytes([0x03]), "关机成功"),
        (bytes([0x80]), "ACK应答"),
    ]
    
    for data, description in test_data:
        checksum = calculate_checksum(data)
        print(f"  {description}: 数据={data.hex(' ').upper()}, 校验和=0x{checksum:02X}")


def error_handling_example():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    # 测试连接失败
    print("\n1. 测试无效串口连接:")
    invalid_controller = N100PowerController(port='/dev/invalid_port')
    
    if invalid_controller.connect():
        print("  ❌ 意外连接成功")
        invalid_controller.disconnect()
    else:
        print("  ✅ 正确处理连接失败")
    
    # 测试未连接状态下的操作
    print("\n2. 测试未连接状态下的操作:")
    disconnected_controller = N100PowerController()
    
    if disconnected_controller.set_led_normal():
        print("  ❌ 意外操作成功")
    else:
        print("  ✅ 正确处理未连接状态")
    
    # 测试超时处理
    print("\n3. 测试超时处理:")
    timeout_controller = N100PowerController(timeout=0.1, max_retries=2)
    
    if timeout_controller.connect():
        print("  连接成功，测试超时...")
        
        # 这可能会超时，因为超时时间很短
        if timeout_controller.set_led_normal():
            print("  ✅ 命令执行成功")
        else:
            print("  ⚠️  命令可能超时（这是预期的）")
        
        timeout_controller.disconnect()
    else:
        print("  ❌ 连接失败")


def performance_test_example():
    """性能测试示例"""
    print("\n=== 性能测试示例 ===")
    
    controller = N100PowerController()
    
    if not controller.connect():
        print("❌ 无法连接串口")
        return False
    
    try:
        print("\n执行性能测试...")
        
        # 测试连续命令执行
        start_time = time.time()
        success_count = 0
        total_commands = 10
        
        for i in range(total_commands):
            print(f"  执行命令 {i+1}/{total_commands}...")
            
            if i % 2 == 0:
                success = controller.set_led_normal()
            else:
                success = controller.set_led_breath()
            
            if success:
                success_count += 1
            
            time.sleep(0.5)  # 避免过快发送
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n性能测试结果:")
        print(f"  总命令数: {total_commands}")
        print(f"  成功命令数: {success_count}")
        print(f"  成功率: {success_count/total_commands*100:.1f}%")
        print(f"  总耗时: {total_time:.2f}秒")
        print(f"  平均每命令耗时: {total_time/total_commands:.2f}秒")
        
        return True
        
    finally:
        controller.disconnect()


def main():
    """主函数"""
    print("N100电源控制器高级使用示例")
    print("=" * 50)
    
    try:
        # 高级回调示例
        if not advanced_callback_example():
            print("❌ 高级回调示例失败")
        
        time.sleep(1)
        
        # 自定义命令示例
        if not custom_command_example():
            print("❌ 自定义命令示例失败")
        
        time.sleep(1)
        
        # 协议帧操作示例
        protocol_frame_example()
        
        time.sleep(1)
        
        # 错误处理示例
        error_handling_example()
        
        time.sleep(1)
        
        # 性能测试示例
        if not performance_test_example():
            print("❌ 性能测试示例失败")
        
        print("\n🎉 所有高级示例执行完成!")
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
