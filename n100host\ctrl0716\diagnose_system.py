#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100系统诊断脚本
检查所有可能的问题
"""

import os
import sys
import subprocess
import socket
import time


def run_command(cmd):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return -1, "", str(e)


def check_service_status():
    """检查服务状态"""
    print("=== 检查服务状态 ===")
    
    services = ["n100-serial-proxy", "n100-shutdown-proxy"]
    
    for service in services:
        print(f"\n{service}:")
        
        # 检查是否active
        rc, stdout, stderr = run_command(f"systemctl is-active {service}")
        print(f"  状态: {stdout.strip()}")
        
        # 检查详细状态
        rc, stdout, stderr = run_command(f"systemctl status {service} --no-pager -l")
        if rc == 0:
            lines = stdout.split('\n')
            for line in lines[:10]:  # 只显示前10行
                if line.strip():
                    print(f"  {line}")
        
        # 检查最近的日志
        print(f"  最近日志:")
        rc, stdout, stderr = run_command(f"journalctl -u {service} -n 3 --no-pager")
        if rc == 0:
            for line in stdout.split('\n'):
                if line.strip():
                    print(f"    {line}")


def check_socket_file():
    """检查Socket文件"""
    print("\n=== 检查Socket文件 ===")
    
    socket_path = "/tmp/n100_serial_proxy.sock"
    
    if os.path.exists(socket_path):
        print(f"✅ Socket文件存在: {socket_path}")
        
        # 检查权限
        stat_info = os.stat(socket_path)
        print(f"  权限: {oct(stat_info.st_mode)[-3:]}")
        print(f"  所有者: UID={stat_info.st_uid}, GID={stat_info.st_gid}")
        
        # 尝试连接
        try:
            sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
            sock.settimeout(2)
            sock.connect(socket_path)
            print("✅ Socket可以连接")
            sock.close()
        except Exception as e:
            print(f"❌ Socket连接失败: {e}")
    else:
        print(f"❌ Socket文件不存在: {socket_path}")


def check_serial_device():
    """检查串口设备"""
    print("\n=== 检查串口设备 ===")
    
    device_path = "/dev/ttyS4"
    
    if os.path.exists(device_path):
        print(f"✅ 串口设备存在: {device_path}")
        
        # 检查权限
        stat_info = os.stat(device_path)
        print(f"  权限: {oct(stat_info.st_mode)[-3:]}")
        print(f"  所有者: UID={stat_info.st_uid}, GID={stat_info.st_gid}")
        
        # 检查是否被占用
        rc, stdout, stderr = run_command(f"lsof {device_path}")
        if rc == 0 and stdout.strip():
            print(f"  设备被占用:")
            for line in stdout.split('\n'):
                if line.strip():
                    print(f"    {line}")
        else:
            print(f"  设备未被占用")
    else:
        print(f"❌ 串口设备不存在: {device_path}")


def check_processes():
    """检查相关进程"""
    print("\n=== 检查相关进程 ===")
    
    # 检查Python进程
    rc, stdout, stderr = run_command("ps aux | grep -E '(serial_proxy|shutdown_daemon)' | grep -v grep")
    if stdout.strip():
        print("相关Python进程:")
        for line in stdout.split('\n'):
            if line.strip():
                print(f"  {line}")
    else:
        print("未找到相关Python进程")


def check_network():
    """检查网络和Socket"""
    print("\n=== 检查网络和Socket ===")
    
    # 检查Unix Socket
    rc, stdout, stderr = run_command("netstat -lx | grep n100")
    if stdout.strip():
        print("Unix Socket监听:")
        for line in stdout.split('\n'):
            if line.strip():
                print(f"  {line}")
    else:
        print("未找到相关Unix Socket")


def check_logs():
    """检查详细日志"""
    print("\n=== 检查详细日志 ===")
    
    print("串口代理最近错误:")
    rc, stdout, stderr = run_command("journalctl -u n100-serial-proxy --since '10 minutes ago' | grep -i error")
    if stdout.strip():
        for line in stdout.split('\n'):
            if line.strip():
                print(f"  {line}")
    else:
        print("  无错误日志")
    
    print("\n关机守护进程最近错误:")
    rc, stdout, stderr = run_command("journalctl -u n100-shutdown-proxy --since '10 minutes ago' | grep -i error")
    if stdout.strip():
        for line in stdout.split('\n'):
            if line.strip():
                print(f"  {line}")
    else:
        print("  无错误日志")


def test_manual_connection():
    """手动测试连接"""
    print("\n=== 手动测试连接 ===")
    
    socket_path = "/tmp/n100_serial_proxy.sock"
    
    try:
        print("尝试连接到串口代理...")
        sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect(socket_path)
        
        print("✅ 连接成功")
        
        # 发送注册消息
        import json
        register_msg = {
            "msg_type": "register",
            "client_id": "diagnostic_test",
            "data": ""
        }
        
        message = json.dumps(register_msg) + '\n'
        sock.send(message.encode('utf-8'))
        
        print("✅ 注册消息已发送")
        
        # 等待响应
        sock.settimeout(2)
        response = sock.recv(1024)
        print(f"✅ 收到响应: {response.decode('utf-8').strip()}")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")


def main():
    """主函数"""
    print("N100系统诊断")
    print("=" * 50)
    
    check_service_status()
    check_socket_file()
    check_serial_device()
    check_processes()
    check_network()
    check_logs()
    test_manual_connection()
    
    print("\n" + "=" * 50)
    print("诊断完成")
    
    print("\n建议的修复步骤:")
    print("1. 如果Socket文件不存在或无法连接，重启串口代理服务")
    print("2. 如果串口设备被占用，停止占用进程")
    print("3. 如果权限有问题，检查用户组和权限设置")
    print("4. 查看详细日志了解具体错误原因")


if __name__ == "__main__":
    main()
