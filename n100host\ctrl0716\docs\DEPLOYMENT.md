# N100电源控制系统部署指南

## 概述

本文档详细介绍了N100电源控制系统的部署过程，包括环境准备、架构选择、安装步骤和验证方法。

## 系统要求

### 硬件要求

- **N100主机**: 支持串口通信的N100设备
- **串口设备**: /dev/ttyS4 或其他可用串口
- **电源板**: 支持N100通信协议的电源管理板
- **内存**: 至少64MB可用内存
- **存储**: 至少100MB可用存储空间

### 软件要求

- **操作系统**: Ubuntu 18.04+ 或其他Linux发行版
- **Python**: 3.6或更高版本
- **权限**: root权限（用于安装系统服务）
- **网络**: 可选，用于下载依赖包

### 依赖包

- **Python包**: pyserial
- **系统工具**: systemctl, journalctl
- **开发工具**: gcc, make（可选，用于编译）

## 架构选择

### 代理架构 vs 直接架构

| 特性 | 代理架构 | 直接架构 |
|------|----------|----------|
| **复杂度** | 中等 | 简单 |
| **串口冲突** | 无冲突 | 可能冲突 |
| **多进程支持** | 支持 | 不支持 |
| **资源占用** | 稍高 | 较低 |
| **故障恢复** | 较好 | 一般 |
| **部署难度** | 中等 | 简单 |

### 推荐选择

- **生产环境**: 推荐代理架构，提供更好的稳定性和扩展性
- **测试环境**: 可选择直接架构，部署简单
- **开发环境**: 推荐代理架构，便于调试和测试

## 环境准备

### 1. 检查系统环境

```bash
# 检查操作系统版本
cat /etc/os-release

# 检查Python版本
python3 --version

# 检查串口设备
ls -l /dev/ttyS*

# 检查权限
groups $USER
```

### 2. 安装依赖

```bash
# 更新包管理器
sudo apt update

# 安装Python和pip
sudo apt install python3 python3-pip

# 安装系统工具
sudo apt install systemd

# 安装Python依赖
pip3 install pyserial
```

### 3. 配置串口权限

```bash
# 添加用户到dialout组
sudo usermod -a -G dialout $USER

# 设置串口权限
sudo chmod 666 /dev/ttyS4

# 创建udev规则（可选）
echo 'KERNEL=="ttyS4", MODE="0666"' | sudo tee /etc/udev/rules.d/99-n100-serial.rules
sudo udevadm control --reload-rules
```

### 4. 验证环境

```bash
# 测试Python串口访问
python3 -c "import serial; print('串口模块可用')"

# 测试串口设备
python3 -c "
import serial
try:
    ser = serial.Serial('/dev/ttyS4', 115200, timeout=1)
    print('串口设备可访问')
    ser.close()
except Exception as e:
    print(f'串口访问失败: {e}')
"
```

## 部署步骤

### 代理架构部署

#### 1. 下载和准备

```bash
# 进入项目目录
cd /path/to/n100host/ctrl0716

# 检查文件完整性
ls -la src/
ls -la services/
ls -la scripts/
```

#### 2. 执行安装脚本

```bash
# 运行代理架构安装脚本
sudo ./scripts/install_proxy_system.sh
```

#### 3. 验证安装

```bash
# 检查服务状态
systemctl status n100-serial-proxy
systemctl status n100-shutdown-proxy

# 检查进程
ps aux | grep n100

# 检查日志
journalctl -u n100-serial-proxy -n 10
journalctl -u n100-shutdown-proxy -n 10
```

#### 4. 测试功能

```bash
# 运行系统测试
n100-test-proxy

# 手动测试
cd /opt/n100/ctrl
python3 power_ctrl_cli.py --use-proxy led normal
```

### 直接架构部署

#### 1. 执行安装脚本

```bash
# 运行直接架构安装脚本
sudo ./scripts/install_direct_system.sh
```

#### 2. 验证安装

```bash
# 检查服务状态
systemctl status n100-shutdown-direct

# 检查进程
ps aux | grep n100_shutdown_daemon_direct

# 检查日志
journalctl -u n100-shutdown-direct -n 10
```

#### 3. 测试功能

```bash
# 运行系统测试
n100-test-direct

# 手动测试
cd /opt/n100/ctrl
python3 power_ctrl_cli.py led normal
```

## 配置管理

### 服务配置

#### 代理架构服务配置

**串口代理服务** (`/etc/systemd/system/n100-serial-proxy.service`):
```ini
[Unit]
Description=N100 Serial Proxy Daemon
After=multi-user.target

[Service]
Type=simple
ExecStart=/usr/bin/python3 /opt/n100/ctrl/serial_proxy_daemon.py
Restart=always
RestartSec=5
User=root

[Install]
WantedBy=multi-user.target
```

**关机代理服务** (`/etc/systemd/system/n100-shutdown-proxy.service`):
```ini
[Unit]
Description=N100 Shutdown Daemon (Proxy Version)
After=n100-serial-proxy.service
Requires=n100-serial-proxy.service

[Service]
Type=simple
ExecStart=/usr/bin/python3 /opt/n100/ctrl/n100_shutdown_daemon_proxy.py
Restart=always
RestartSec=5
User=root

[Install]
WantedBy=multi-user.target
```

#### 直接架构服务配置

**直接关机服务** (`/etc/systemd/system/n100-shutdown-direct.service`):
```ini
[Unit]
Description=N100 Shutdown Daemon (Direct Serial)
After=multi-user.target

[Service]
Type=simple
ExecStart=/usr/bin/python3 /opt/n100/ctrl/n100_shutdown_daemon_direct.py
Restart=always
RestartSec=5
User=root

[Install]
WantedBy=multi-user.target
```

### 参数调优

#### 串口参数

```python
# 在源代码中调整串口参数
SERIAL_PORT = '/dev/ttyS4'
BAUDRATE = 115200
TIMEOUT = 1.0
MAX_RETRIES = 10
```

#### 系统参数

```bash
# 调整系统日志级别
sudo systemctl edit n100-serial-proxy
```

添加内容：
```ini
[Service]
Environment=PYTHONUNBUFFERED=1
Environment=LOG_LEVEL=INFO
```

## 监控和维护

### 服务监控

```bash
# 检查服务状态
systemctl is-active n100-serial-proxy
systemctl is-active n100-shutdown-proxy

# 查看服务详细状态
systemctl status n100-serial-proxy -l
systemctl status n100-shutdown-proxy -l

# 监控服务日志
journalctl -u n100-serial-proxy -f
journalctl -u n100-shutdown-proxy -f
```

### 性能监控

```bash
# 监控进程资源使用
top -p $(pgrep -f "serial_proxy_daemon")
top -p $(pgrep -f "n100_shutdown_daemon")

# 监控串口使用
lsof /dev/ttyS4

# 监控网络连接（代理模式）
ss -x | grep n100_serial_proxy
```

### 日志管理

```bash
# 查看系统日志
journalctl -u n100-serial-proxy --since "1 hour ago"
journalctl -u n100-shutdown-proxy --since "1 hour ago"

# 清理旧日志
sudo journalctl --vacuum-time=7d

# 设置日志大小限制
sudo systemctl edit systemd-journald
```

添加内容：
```ini
[Journal]
SystemMaxUse=100M
RuntimeMaxUse=50M
```

### 备份和恢复

#### 备份配置

```bash
# 创建备份目录
sudo mkdir -p /backup/n100

# 备份服务配置
sudo cp /etc/systemd/system/n100-*.service /backup/n100/

# 备份源代码
sudo tar -czf /backup/n100/n100-ctrl-$(date +%Y%m%d).tar.gz /opt/n100/ctrl

# 备份日志
sudo journalctl -u n100-serial-proxy --since "1 week ago" > /backup/n100/proxy.log
sudo journalctl -u n100-shutdown-proxy --since "1 week ago" > /backup/n100/shutdown.log
```

#### 恢复配置

```bash
# 停止服务
sudo systemctl stop n100-serial-proxy n100-shutdown-proxy

# 恢复服务配置
sudo cp /backup/n100/*.service /etc/systemd/system/

# 恢复源代码
sudo tar -xzf /backup/n100/n100-ctrl-*.tar.gz -C /

# 重新加载配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start n100-serial-proxy n100-shutdown-proxy
```

## 故障排除

### 常见问题

#### 1. 串口权限问题

**症状**: 无法打开串口设备
**解决方法**:
```bash
sudo chmod 666 /dev/ttyS4
sudo usermod -a -G dialout $USER
# 重新登录或重启
```

#### 2. 服务启动失败

**症状**: systemctl status显示失败
**解决方法**:
```bash
# 查看详细错误
journalctl -u n100-serial-proxy -n 20

# 检查Python路径
which python3

# 检查文件权限
ls -la /opt/n100/ctrl/
```

#### 3. 通信超时

**症状**: 命令执行超时
**解决方法**:
```bash
# 检查串口连接
dmesg | grep ttyS4

# 测试串口通信
echo "test" > /dev/ttyS4

# 增加超时时间
# 修改源代码中的TIMEOUT参数
```

#### 4. 代理连接失败

**症状**: 代理客户端无法连接
**解决方法**:
```bash
# 检查Socket文件
ls -la /tmp/n100_serial_proxy.sock

# 检查代理服务状态
systemctl status n100-serial-proxy

# 重启代理服务
sudo systemctl restart n100-serial-proxy
```

### 调试工具

```bash
# 串口调试
sudo minicom -D /dev/ttyS4 -b 115200

# 进程调试
strace -p $(pgrep -f "serial_proxy_daemon")

# 网络调试
socat - UNIX-CONNECT:/tmp/n100_serial_proxy.sock

# 系统调试
dmesg | tail -20
```

## 升级和迁移

### 升级步骤

```bash
# 1. 备份当前配置
sudo ./scripts/backup_system.sh

# 2. 停止服务
sudo systemctl stop n100-serial-proxy n100-shutdown-proxy

# 3. 更新代码
sudo cp -r new_version/src/* /opt/n100/ctrl/

# 4. 重启服务
sudo systemctl start n100-serial-proxy n100-shutdown-proxy

# 5. 验证升级
./scripts/test_system.sh
```

### 架构迁移

#### 从直接架构迁移到代理架构

```bash
# 1. 卸载直接架构
sudo ./scripts/uninstall.sh

# 2. 安装代理架构
sudo ./scripts/install_proxy_system.sh

# 3. 更新应用配置
# 修改应用代码使用代理模式
```

---

**版本**: 1.0.0  
**最后更新**: 2025-07-16  
**维护者**: N100 Team
