# N100电源控制系统 Makefile
# 提供常用的开发和部署命令

.PHONY: help install install-proxy install-direct uninstall test test-unit test-integration clean lint format docs backup

# 默认目标
.DEFAULT_GOAL := help

# 项目配置
PROJECT_NAME := n100-power-ctrl
VERSION := 1.0.0
PYTHON := python3
PIP := pip3

# 路径配置
SRC_DIR := src
TESTS_DIR := tests
DOCS_DIR := docs
SCRIPTS_DIR := scripts
SERVICES_DIR := services
EXAMPLES_DIR := examples

# 安装路径
INSTALL_DIR := /opt/n100/ctrl
SERVICE_DIR := /etc/systemd/system
LOG_DIR := /var/log

help: ## 显示帮助信息
	@echo "N100电源控制系统 - 可用命令:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "示例:"
	@echo "  make install-proxy    # 安装代理架构"
	@echo "  make test            # 运行所有测试"
	@echo "  make clean           # 清理临时文件"

# 安装相关命令
install: ## 交互式选择架构并安装
	@echo "请选择要安装的架构:"
	@echo "1) 代理架构 (推荐)"
	@echo "2) 直接架构"
	@read -p "请输入选择 (1-2): " choice; \
	case $$choice in \
		1) $(MAKE) install-proxy ;; \
		2) $(MAKE) install-direct ;; \
		*) echo "无效选择" && exit 1 ;; \
	esac

install-proxy: ## 安装代理架构
	@echo "安装代理架构..."
	@sudo $(SCRIPTS_DIR)/install_proxy_system.sh
	@echo "代理架构安装完成"

install-direct: ## 安装直接架构
	@echo "安装直接架构..."
	@sudo $(SCRIPTS_DIR)/install_direct_system.sh
	@echo "直接架构安装完成"

uninstall: ## 卸载系统
	@echo "卸载N100电源控制系统..."
	@sudo $(SCRIPTS_DIR)/uninstall.sh
	@echo "系统卸载完成"

# 测试相关命令
test: ## 运行所有测试
	@echo "运行所有测试..."
	@$(PYTHON) $(TESTS_DIR)/run_all_tests.py

test-unit: ## 运行单元测试
	@echo "运行单元测试..."
	@$(PYTHON) $(TESTS_DIR)/test_protocol.py
	@$(PYTHON) $(TESTS_DIR)/test_power_ctrl.py

test-integration: ## 运行集成测试
	@echo "运行集成测试..."
	@$(PYTHON) $(TESTS_DIR)/test_integration.py

test-system: ## 运行系统测试
	@echo "运行系统测试..."
	@$(SCRIPTS_DIR)/test_system.sh

# 开发相关命令
deps: ## 安装依赖包
	@echo "安装Python依赖..."
	@$(PIP) install -r requirements.txt

deps-dev: ## 安装开发依赖
	@echo "安装开发依赖..."
	@$(PIP) install -r requirements.txt
	@$(PIP) install pytest pytest-cov flake8 black isort mypy

lint: ## 代码检查
	@echo "运行代码检查..."
	@flake8 $(SRC_DIR) $(TESTS_DIR) --max-line-length=100
	@echo "代码检查完成"

format: ## 代码格式化
	@echo "格式化代码..."
	@black $(SRC_DIR) $(TESTS_DIR) --line-length=100
	@isort $(SRC_DIR) $(TESTS_DIR)
	@echo "代码格式化完成"

type-check: ## 类型检查
	@echo "运行类型检查..."
	@mypy $(SRC_DIR) --ignore-missing-imports
	@echo "类型检查完成"

# 示例相关命令
examples: ## 运行示例
	@echo "运行基础示例..."
	@$(PYTHON) $(EXAMPLES_DIR)/basic_example.py
	@echo ""
	@echo "运行高级示例..."
	@$(PYTHON) $(EXAMPLES_DIR)/advanced_example.py

example-basic: ## 运行基础示例
	@$(PYTHON) $(EXAMPLES_DIR)/basic_example.py

example-advanced: ## 运行高级示例
	@$(PYTHON) $(EXAMPLES_DIR)/advanced_example.py

# 服务管理命令
status: ## 查看服务状态
	@echo "检查服务状态..."
	@systemctl status n100-serial-proxy n100-shutdown-proxy n100-shutdown-direct 2>/dev/null || true

start: ## 启动服务
	@echo "启动服务..."
	@sudo systemctl start n100-serial-proxy 2>/dev/null || true
	@sudo systemctl start n100-shutdown-proxy 2>/dev/null || true
	@sudo systemctl start n100-shutdown-direct 2>/dev/null || true

stop: ## 停止服务
	@echo "停止服务..."
	@sudo systemctl stop n100-serial-proxy 2>/dev/null || true
	@sudo systemctl stop n100-shutdown-proxy 2>/dev/null || true
	@sudo systemctl stop n100-shutdown-direct 2>/dev/null || true

restart: ## 重启服务
	@echo "重启服务..."
	@sudo systemctl restart n100-serial-proxy 2>/dev/null || true
	@sudo systemctl restart n100-shutdown-proxy 2>/dev/null || true
	@sudo systemctl restart n100-shutdown-direct 2>/dev/null || true

logs: ## 查看服务日志
	@echo "查看服务日志..."
	@sudo journalctl -u n100-serial-proxy -u n100-shutdown-proxy -u n100-shutdown-direct -f

# 文档相关命令
docs: ## 生成文档
	@echo "生成文档..."
	@if command -v sphinx-build >/dev/null 2>&1; then \
		sphinx-build -b html $(DOCS_DIR) $(DOCS_DIR)/_build/html; \
		echo "文档已生成到 $(DOCS_DIR)/_build/html"; \
	else \
		echo "Sphinx未安装，跳过文档生成"; \
	fi

docs-serve: ## 启动文档服务器
	@echo "启动文档服务器..."
	@if [ -d "$(DOCS_DIR)/_build/html" ]; then \
		cd $(DOCS_DIR)/_build/html && $(PYTHON) -m http.server 8000; \
	else \
		echo "请先运行 'make docs' 生成文档"; \
	fi

# 清理相关命令
clean: ## 清理临时文件
	@echo "清理临时文件..."
	@find . -type f -name "*.pyc" -delete
	@find . -type d -name "__pycache__" -delete
	@find . -type f -name "*.log" -delete
	@find . -type f -name "test_report.txt" -delete
	@rm -rf .pytest_cache
	@rm -rf .mypy_cache
	@rm -rf $(DOCS_DIR)/_build
	@echo "清理完成"

clean-all: clean ## 清理所有生成文件
	@echo "清理所有生成文件..."
	@rm -rf build/
	@rm -rf dist/
	@rm -rf *.egg-info/
	@echo "深度清理完成"

# 备份相关命令
backup: ## 备份系统配置
	@echo "备份系统配置..."
	@mkdir -p backup/$(shell date +%Y%m%d_%H%M%S)
	@if [ -d "$(INSTALL_DIR)" ]; then \
		sudo cp -r $(INSTALL_DIR) backup/$(shell date +%Y%m%d_%H%M%S)/; \
	fi
	@if [ -f "$(SERVICE_DIR)/n100-*.service" ]; then \
		sudo cp $(SERVICE_DIR)/n100-*.service backup/$(shell date +%Y%m%d_%H%M%S)/ 2>/dev/null || true; \
	fi
	@echo "备份完成"

# 打包相关命令
package: ## 创建发布包
	@echo "创建发布包..."
	@$(PYTHON) setup.py sdist bdist_wheel
	@echo "发布包已创建到 dist/ 目录"

install-package: ## 安装Python包
	@echo "安装Python包..."
	@$(PIP) install -e .
	@echo "Python包安装完成"

# 部署相关命令
deploy-dev: ## 部署到开发环境
	@echo "部署到开发环境..."
	@$(MAKE) clean
	@$(MAKE) deps-dev
	@$(MAKE) test
	@echo "开发环境部署完成"

deploy-prod: ## 部署到生产环境
	@echo "部署到生产环境..."
	@$(MAKE) clean
	@$(MAKE) test
	@$(MAKE) install-proxy
	@$(MAKE) status
	@echo "生产环境部署完成"

# 维护相关命令
check-health: ## 健康检查
	@echo "执行健康检查..."
	@$(SCRIPTS_DIR)/test_system.sh

monitor: ## 监控系统状态
	@echo "监控系统状态..."
	@watch -n 5 'systemctl status n100-serial-proxy n100-shutdown-proxy n100-shutdown-direct 2>/dev/null || true'

# 工具命令
serial-test: ## 测试串口通信
	@echo "测试串口通信..."
	@$(PYTHON) -c "
import serial
try:
    ser = serial.Serial('/dev/ttyS4', 115200, timeout=1)
    print('串口连接成功')
    ser.close()
except Exception as e:
    print(f'串口连接失败: {e}')
"

permissions: ## 修复权限
	@echo "修复权限..."
	@sudo chmod 666 /dev/ttyS4 2>/dev/null || echo "串口设备不存在"
	@sudo usermod -a -G dialout $$USER
	@echo "权限修复完成，请重新登录生效"

version: ## 显示版本信息
	@echo "N100电源控制系统 v$(VERSION)"
	@echo "Python: $(shell $(PYTHON) --version)"
	@echo "系统: $(shell uname -a)"

info: ## 显示系统信息
	@echo "=== N100电源控制系统信息 ==="
	@echo "版本: $(VERSION)"
	@echo "Python: $(shell $(PYTHON) --version)"
	@echo "安装目录: $(INSTALL_DIR)"
	@echo "服务目录: $(SERVICE_DIR)"
	@echo "日志目录: $(LOG_DIR)"
	@echo ""
	@echo "=== 服务状态 ==="
	@systemctl is-active n100-serial-proxy 2>/dev/null && echo "串口代理: 运行中" || echo "串口代理: 未运行"
	@systemctl is-active n100-shutdown-proxy 2>/dev/null && echo "关机代理: 运行中" || echo "关机代理: 未运行"
	@systemctl is-active n100-shutdown-direct 2>/dev/null && echo "直接关机: 运行中" || echo "直接关机: 未运行"
	@echo ""
	@echo "=== 串口状态 ==="
	@ls -la /dev/ttyS4 2>/dev/null || echo "串口设备不存在"
