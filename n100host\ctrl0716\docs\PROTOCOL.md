# N100电源控制系统通信协议

## 概述

N100电源控制系统使用自定义的串口通信协议，在N100主机和电源板之间进行数据交换。协议采用帧结构，包含帧头、长度、命令、数据、校验和、帧尾等字段。

## 协议规范

### 消息帧格式

```
+--------+--------+--------+--------+----------+--------+
| 帧头   | 长度   | 命令   | 数据   | 校验和   | 帧尾   |
| 0xAA   | 1字节  | 1字节  | N字节  | 1字节    | 0x55   |
+--------+--------+--------+--------+----------+--------+
```

### 字段说明

| 字段 | 长度 | 说明 |
|------|------|------|
| 帧头 | 1字节 | 固定值 0xAA，标识帧开始 |
| 长度 | 1字节 | 数据长度，包含命令字节，不包含帧头、长度、校验和、帧尾 |
| 命令 | 1字节 | 命令类型，定义具体操作 |
| 数据 | N字节 | 命令数据，长度由长度字段指定 |
| 校验和 | 1字节 | 对命令+数据进行校验和计算 |
| 帧尾 | 1字节 | 固定值 0x55，标识帧结束 |

### 校验和计算

校验和采用二进制补码方法：

1. 对命令字节和数据字节求和
2. 取低8位
3. 按位取反
4. 加1
5. 取低8位作为最终校验和

**公式**: `checksum = ((~sum(command + data)) + 1) & 0xFF`

**示例**:
- 数据: `[0x01, 0x00]` (LED正常模式)
- 求和: `0x01 + 0x00 = 0x01`
- 取反: `~0x01 = 0xFE`
- 加1: `0xFE + 1 = 0xFF`
- 校验和: `0xFF`

## 命令定义

### 命令类型

| 命令 | 值 | 方向 | 说明 |
|------|----|----|------|
| LED_MODE | 0x01 | N100→电源板 | 设置LED模式 |
| BREATH_PERIOD | 0x02 | N100→电源板 | 设置呼吸灯周期 |
| SHUTDOWN_SUCCESS | 0x03 | N100→电源板 | 关机成功通知 |
| SHUTDOWN_REQ | 0x13 | 电源板→N100 | 关机请求 |
| ACK | 0x80 | 双向 | 通用应答 |

### LED模式命令 (0x01)

设置LED的工作模式。

**数据格式**:
- 长度: 2字节 (命令1字节 + 数据1字节)
- 数据: 1字节模式值

**模式值**:
| 值 | 模式 | 说明 |
|----|------|------|
| 0x00 | 正常模式 | LED常亮 |
| 0x01 | 呼吸模式 | LED呼吸闪烁 |

**示例帧**:
- 正常模式: `AA 02 01 00 FF 55`
- 呼吸模式: `AA 02 01 01 FE 55`

### 呼吸周期命令 (0x02)

设置呼吸灯的闪烁周期。

**数据格式**:
- 长度: 2字节 (命令1字节 + 数据1字节)
- 数据: 1字节周期值（秒）

**周期值**:
| 值 | 周期 | 说明 |
|----|------|------|
| 0x01 | 1秒 | 1秒一个呼吸周期 |
| 0x03 | 3秒 | 3秒一个呼吸周期 |
| 0x05 | 5秒 | 5秒一个呼吸周期 |

**示例帧**:
- 1秒周期: `AA 02 02 01 FD 55`
- 3秒周期: `AA 02 02 03 FB 55`
- 5秒周期: `AA 02 02 05 F9 55`

### 关机成功命令 (0x03)

通知电源板N100已成功关机。

**数据格式**:
- 长度: 1字节 (仅命令字节)
- 数据: 无

**示例帧**:
- 关机成功: `AA 01 03 FD 55`

### 关机请求命令 (0x13)

电源板请求N100关机。

**数据格式**:
- 长度: 1字节 (仅命令字节)
- 数据: 无

**示例帧**:
- 关机请求: `AA 01 13 ED 55`

### ACK应答命令 (0x80)

通用应答，确认收到消息。

**数据格式**:
- 长度: 1字节 (仅命令字节)
- 数据: 无

**示例帧**:
- ACK应答: `AA 01 80 80 55`

## 通信流程

### 基本通信流程

1. **发送方**发送命令帧
2. **接收方**验证帧格式和校验和
3. **接收方**发送ACK应答帧
4. **发送方**收到ACK，确认通信成功

### 超时和重试

- **超时时间**: 默认1秒
- **重试次数**: 默认10次
- **重试间隔**: 立即重试

### 错误处理

1. **校验和错误**: 忽略帧，不发送ACK
2. **格式错误**: 忽略帧，不发送ACK
3. **超时**: 重新发送原帧
4. **达到最大重试次数**: 返回失败

## 协议限制

### 数据长度限制

- **最小帧长度**: 5字节 (无数据帧)
- **最大帧长度**: 20字节
- **最大数据长度**: 13字节

### 性能参数

- **波特率**: 115200 bps
- **数据位**: 8位
- **停止位**: 1位
- **校验位**: 无
- **流控制**: 无

## 实现注意事项

### 发送端

1. 严格按照帧格式构造消息
2. 正确计算校验和
3. 等待ACK应答
4. 实现超时重试机制

### 接收端

1. 验证帧头和帧尾
2. 验证帧长度
3. 验证校验和
4. 及时发送ACK应答

### 调试建议

1. 记录所有发送和接收的帧数据
2. 验证校验和计算
3. 监控超时和重试情况
4. 检查串口配置参数

## 示例代码

### Python实现

```python
def calculate_checksum(data):
    """计算校验和"""
    checksum = sum(data) & 0xFF
    return ((~checksum) + 1) & 0xFF

def create_frame(command, data=b''):
    """创建协议帧"""
    length = len(data) + 1
    checksum_data = bytes([command]) + data
    checksum = calculate_checksum(checksum_data)
    
    frame = bytes([0xAA, length, command]) + data + bytes([checksum, 0x55])
    return frame

def validate_frame(frame_data):
    """验证协议帧"""
    if len(frame_data) < 5:
        return False
    
    if frame_data[0] != 0xAA or frame_data[-1] != 0x55:
        return False
    
    length = frame_data[1]
    if len(frame_data) != length + 4:
        return False
    
    command = frame_data[2]
    data = frame_data[3:3+length-1] if length > 1 else b''
    expected_checksum = frame_data[3+length-1]
    
    checksum_data = bytes([command]) + data
    actual_checksum = calculate_checksum(checksum_data)
    
    return expected_checksum == actual_checksum
```

### C实现

```c
uint8_t calculate_checksum(uint8_t *data, uint8_t length) {
    uint8_t checksum = 0;
    for (uint8_t i = 0; i < length; i++) {
        checksum += data[i];
    }
    return (~checksum + 1);
}

void create_frame(uint8_t command, uint8_t *data, uint8_t data_len, uint8_t *frame) {
    frame[0] = 0xAA;                    // 帧头
    frame[1] = data_len + 1;            // 长度
    frame[2] = command;                 // 命令
    
    // 复制数据
    for (uint8_t i = 0; i < data_len; i++) {
        frame[3 + i] = data[i];
    }
    
    // 计算校验和
    uint8_t checksum_data[14];
    checksum_data[0] = command;
    for (uint8_t i = 0; i < data_len; i++) {
        checksum_data[i + 1] = data[i];
    }
    
    frame[3 + data_len] = calculate_checksum(checksum_data, data_len + 1);
    frame[4 + data_len] = 0x55;         // 帧尾
}
```

## 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| 1.0.0 | 2025-07-16 | 初始版本，定义基本协议格式和命令 |

---

**维护者**: N100 Team  
**最后更新**: 2025-07-16
