#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试串口管理器修复效果
"""

import os
import sys
import time
import threading

# 添加src和test目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'test'))

try:
    from memory_serial_simulator import MemorySerial, PowerBoardSimulator, CommunicationTester
    from power_board_simulator import PowerBoardSimulator as PBSimulator
    from n100_power_ctrl import N100PowerController
    from serial_manager import SerialManager
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在n100host/ctrl目录下运行此脚本")
    sys.exit(1)


def test_serial_manager_ack_fix():
    """测试串口管理器ACK检测修复"""
    print("=== 测试串口管理器ACK检测修复 ===")

    # 创建内存串口对
    n100_serial = MemorySerial("N100")
    power_serial = MemorySerial("PowerBoard")

    # 连接串口对
    n100_serial.connect_peer(power_serial)

    # 创建电源板模拟器
    power_simulator = PowerBoardSimulator(power_serial)
    
    try:
        # 启动电源板模拟器
        power_simulator.start()
        time.sleep(0.5)
        
        # 创建串口管理器并替换串口
        manager = SerialManager()
        manager.serial = n100_serial
        manager.is_running = True
        
        # 启动读取线程
        manager._read_thread = threading.Thread(target=manager._read_loop, daemon=True)
        manager._read_thread.start()
        
        # 注册测试客户端
        client_id = "test_client"
        messages_received = []
        
        def message_callback(message):
            messages_received.append(message)
            print(f"收到消息: 命令=0x{message.command:02X}")
        
        def error_callback(error):
            print(f"错误: {error}")
        
        success = manager.register_client(
            client_id=client_id,
            message_callback=message_callback,
            error_callback=error_callback
        )
        
        if not success:
            print("❌ 客户端注册失败")
            return False
        
        print("✅ 客户端注册成功")
        
        # 测试发送LED命令
        print("\n测试发送LED正常模式命令...")
        result = manager.send_message(
            client_id=client_id,
            command=0x01,  # LED模式命令
            data=bytes([0x00]),  # 正常模式
            wait_ack=True,
            max_retries=3
        )
        
        if result:
            print("✅ LED命令发送成功")
        else:
            print("❌ LED命令发送失败")
        
        # 等待一段时间让消息处理完成
        time.sleep(1)
        
        # 测试发送呼吸模式命令
        print("\n测试发送LED呼吸模式命令...")
        result = manager.send_message(
            client_id=client_id,
            command=0x01,  # LED模式命令
            data=bytes([0x01]),  # 呼吸模式
            wait_ack=True,
            max_retries=3
        )
        
        if result:
            print("✅ 呼吸模式命令发送成功")
        else:
            print("❌ 呼吸模式命令发送失败")
        
        # 等待一段时间
        time.sleep(1)
        
        # 检查统计信息
        stats = power_simulator.get_stats()
        print(f"\n电源板模拟器统计:")
        print(f"  接收命令数: {stats['received_commands']}")
        print(f"  发送ACK数: {stats['sent_acks']}")
        
        # 注销客户端
        manager.unregister_client(client_id)
        
        # 停止管理器
        manager.is_running = False
        
        return result
        
    except Exception as e:
        print(f"测试异常: {e}")
        return False
    
    finally:
        # 清理资源
        power_simulator.stop()


def test_power_controller_with_manager():
    """测试电源控制器使用串口管理器"""
    print("\n=== 测试电源控制器使用串口管理器 ===")

    # 创建内存串口对
    n100_serial = MemorySerial("N100")
    power_serial = MemorySerial("PowerBoard")

    # 连接串口对
    n100_serial.connect_peer(power_serial)

    # 创建电源板模拟器
    power_simulator = PowerBoardSimulator(power_serial)
    
    try:
        # 启动电源板模拟器
        power_simulator.start()
        time.sleep(0.5)
        
        # 创建电源控制器
        controller = N100PowerController(use_manager=True, max_retries=3, timeout=2.0)
        
        # 替换串口管理器的串口
        if controller.manager:
            controller.manager.serial = n100_serial
            controller.manager.is_running = True
            controller.manager._read_thread = threading.Thread(
                target=controller.manager._read_loop, daemon=True
            )
            controller.manager._read_thread.start()
        
        controller.is_connected = True
        
        # 测试命令序列
        commands = [
            ("LED正常模式", lambda: controller.set_led_normal()),
            ("LED呼吸模式", lambda: controller.set_led_breath()),
            ("呼吸周期3秒", lambda: controller.set_breath_3s()),
        ]
        
        success_count = 0
        for desc, cmd_func in commands:
            print(f"\n执行: {desc}")
            if cmd_func():
                success_count += 1
                print(f"✅ {desc} - 成功")
            else:
                print(f"❌ {desc} - 失败")
            
            time.sleep(0.5)
        
        print(f"\n测试完成: {success_count}/{len(commands)} 个命令成功")
        
        # 检查统计信息
        stats = power_simulator.get_stats()
        print(f"\n电源板模拟器统计:")
        print(f"  接收命令数: {stats['received_commands']}")
        print(f"  发送ACK数: {stats['sent_acks']}")
        
        return success_count == len(commands)
        
    except Exception as e:
        print(f"测试异常: {e}")
        return False
    
    finally:
        # 清理资源
        power_simulator.stop()


def main():
    """主函数"""
    print("开始测试串口管理器修复效果...\n")
    
    # 测试1: 串口管理器ACK检测
    test1_result = test_serial_manager_ack_fix()
    
    # 测试2: 电源控制器使用串口管理器
    test2_result = test_power_controller_with_manager()
    
    # 总结
    print("\n" + "="*50)
    print("测试结果总结:")
    print(f"串口管理器ACK检测: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"电源控制器管理器模式: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！串口管理器修复成功。")
        return 0
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
