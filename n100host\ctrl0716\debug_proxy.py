#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试版串口代理
用于找出Socket服务器问题
"""

import os
import sys
import socket
import threading
import json
import time
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

try:
    from serial_proxy_daemon import ProxyMessage
except ImportError as e:
    print(f"错误: 无法导入模块: {e}")
    sys.exit(1)


class DebugProxyServer:
    """调试版代理服务器"""
    
    def __init__(self, socket_path="/tmp/debug_proxy.sock"):
        self.socket_path = socket_path
        self.running = False
        self.server_socket = None
        self.clients = {}
        
        # 设置详细日志
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('DebugProxy')
    
    def start(self):
        """启动调试代理"""
        try:
            # 删除已存在的Socket文件
            if os.path.exists(self.socket_path):
                os.unlink(self.socket_path)
            
            # 创建Unix Socket
            self.server_socket = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
            self.server_socket.bind(self.socket_path)
            self.server_socket.listen(10)
            
            # 设置Socket权限
            os.chmod(self.socket_path, 0o666)
            
            self.running = True
            
            self.logger.info(f"调试代理启动: {self.socket_path}")
            
            # 启动服务器循环
            self._server_loop()
            
        except Exception as e:
            self.logger.error(f"启动失败: {e}")
            import traceback
            traceback.print_exc()
    
    def stop(self):
        """停止调试代理"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        
        # 删除Socket文件
        if os.path.exists(self.socket_path):
            os.unlink(self.socket_path)
        
        self.logger.info("调试代理已停止")
    
    def _server_loop(self):
        """服务器循环"""
        self.logger.info("开始监听连接...")
        
        while self.running:
            try:
                self.logger.debug("等待客户端连接...")
                client_socket, addr = self.server_socket.accept()
                self.logger.info(f"客户端连接: {addr}")
                
                # 启动客户端处理线程
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket,),
                    daemon=True
                )
                client_thread.start()
                
            except Exception as e:
                if self.running:
                    self.logger.error(f"服务器循环异常: {e}")
                    import traceback
                    traceback.print_exc()
                break
    
    def _handle_client(self, client_socket):
        """处理客户端"""
        client_id = None
        
        try:
            self.logger.info("开始处理客户端")
            
            while self.running:
                try:
                    self.logger.debug("等待客户端数据...")
                    
                    # 设置超时
                    client_socket.settimeout(1.0)
                    data = client_socket.recv(4096)
                    
                    if not data:
                        self.logger.info("客户端断开连接")
                        break
                    
                    self.logger.info(f"收到数据: {data}")
                    
                    try:
                        # 解析消息
                        message_str = data.decode('utf-8').strip()
                        self.logger.debug(f"解析消息: {message_str}")
                        
                        message = ProxyMessage.from_json(message_str)
                        self.logger.info(f"解析成功: 类型={message.msg_type}, 客户端={message.client_id}")
                        
                        if message.msg_type == 'register':
                            # 注册客户端
                            client_id = message.client_id
                            self.clients[client_id] = client_socket
                            
                            self.logger.info(f"客户端注册成功: {client_id}")
                            
                            # 发送注册确认
                            response = ProxyMessage('register_ack', client_id)
                            response_data = response.to_json().encode('utf-8') + b'\n'
                            
                            self.logger.debug(f"发送响应: {response_data}")
                            client_socket.send(response_data)
                            self.logger.info("注册确认已发送")
                        
                        elif message.msg_type == 'send_request':
                            self.logger.info(f"收到发送请求: {message.data.hex(' ').upper()}")
                            # 这里可以模拟处理发送请求
                            self.logger.info("发送请求处理完成")
                        
                        else:
                            self.logger.warning(f"未知消息类型: {message.msg_type}")
                    
                    except json.JSONDecodeError as e:
                        self.logger.error(f"JSON解析失败: {e}")
                        self.logger.error(f"原始数据: {data}")
                    
                    except Exception as e:
                        self.logger.error(f"消息处理异常: {e}")
                        import traceback
                        traceback.print_exc()
                
                except socket.timeout:
                    # 超时是正常的，继续循环
                    continue
                
                except Exception as e:
                    self.logger.error(f"接收数据异常: {e}")
                    break
        
        except Exception as e:
            self.logger.error(f"客户端处理异常: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            # 清理客户端
            if client_id and client_id in self.clients:
                del self.clients[client_id]
                self.logger.info(f"客户端清理: {client_id}")
            
            try:
                client_socket.close()
            except:
                pass


def test_debug_proxy():
    """测试调试代理"""
    print("=== 调试代理测试 ===")
    
    proxy = DebugProxyServer()
    
    try:
        # 在后台启动代理
        proxy_thread = threading.Thread(target=proxy.start, daemon=True)
        proxy_thread.start()
        
        # 等待代理启动
        time.sleep(1)
        
        # 测试连接
        print("测试连接到调试代理...")
        
        sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        sock.connect("/tmp/debug_proxy.sock")
        
        # 发送注册消息
        register_msg = {
            "msg_type": "register",
            "client_id": "debug_test",
            "data": ""
        }
        
        message = json.dumps(register_msg) + '\n'
        print(f"发送: {message.strip()}")
        
        sock.send(message.encode('utf-8'))
        
        # 等待响应
        response = sock.recv(1024)
        print(f"收到: {response.decode('utf-8').strip()}")
        
        sock.close()
        print("✅ 调试代理测试成功")
        
    except Exception as e:
        print(f"❌ 调试代理测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        proxy.stop()


def main():
    """主函数"""
    print("串口代理调试工具")
    print("=" * 30)
    
    print("选择模式:")
    print("1. 运行调试代理服务器")
    print("2. 测试调试代理")
    print("3. 退出")
    
    try:
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            print("启动调试代理服务器...")
            print("按 Ctrl+C 停止")
            
            proxy = DebugProxyServer()
            proxy.start()
        
        elif choice == "2":
            test_debug_proxy()
        
        elif choice == "3":
            print("退出")
        
        else:
            print("无效选择")
    
    except KeyboardInterrupt:
        print("\n程序被中断")
    
    except Exception as e:
        print(f"程序异常: {e}")


if __name__ == "__main__":
    main()
