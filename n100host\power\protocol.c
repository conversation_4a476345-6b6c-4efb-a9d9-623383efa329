#include "protocol.h"
#include "uart.h"

//========================================================================
//                              全局变量
//========================================================================

// 各串口的接收缓冲区
static ProtocolRxBuffer_t g_uart2_rx_buffer;
static ProtocolRxBuffer_t g_uart3_rx_buffer;

// 分别为UART2和UART3设置发送和接收数据
ProtocolTxData_t g_uart2_tx_data;  // UART2发送数据
ProtocolRxData_t g_uart2_rx_data;  // UART2接收数据
ProtocolTxData_t g_uart3_tx_data;  // UART3发送数据
ProtocolRxData_t g_uart3_rx_data;  // UART3接收数据

// 调试变量
u16 g_protocol_debug_frame_count = 0;  // 完整帧计数
u16 g_protocol_debug_process_count = 0; // 处理计数
u8 g_protocol_debug_checksum_fail = 0;  // 校验失败标志
u8 g_protocol_debug_calculated_checksum = 0; // 计算的校验和
u8 g_protocol_debug_received_checksum = 0;   // 接收的校验和

//========================================================================
//                              内部函数
//========================================================================

/***************************************************************************
 * 描  述 : 获取指定串口的接收缓冲区指针
 * 入  参 : uart_num - 串口号(2,3)
 * 返回值 : 接收缓冲区指针
 **************************************************************************/
static ProtocolRxBuffer_t* Protocol_GetRxBuffer(u8 uart_num)
{
    switch(uart_num)
    {
        case 2: return &g_uart2_rx_buffer;
        case 3: return &g_uart3_rx_buffer;
        default: return NULL;
    }
}

/***************************************************************************
 * 描  述 : 获取指定串口的发送数据指针
 * 入  参 : uart_num - 串口号(2,3)
 * 返回值 : 发送数据指针
 **************************************************************************/
static ProtocolTxData_t* Protocol_GetTxData(u8 uart_num)
{
    switch(uart_num)
    {
        case 2: return &g_uart2_tx_data;
        case 3: return &g_uart3_tx_data;
        default: return NULL;
    }
}

/***************************************************************************
 * 描  述 : 获取指定串口的接收数据指针
 * 入  参 : uart_num - 串口号(2,3)
 * 返回值 : 接收数据指针
 **************************************************************************/
static ProtocolRxData_t* Protocol_GetRxData(u8 uart_num)
{
    switch(uart_num)
    {
        case 2: return &g_uart2_rx_data;
        case 3: return &g_uart3_rx_data;
        default: return NULL;
    }
}

/***************************************************************************
 * 描  述 : 重置接收缓冲区
 * 入  参 : rx_buf - 接收缓冲区指针
 * 返回值 : 无
 **************************************************************************/
static void Protocol_ResetRxBuffer(ProtocolRxBuffer_t *rx_buf)
{
    if(rx_buf != NULL)
    {
        rx_buf->index = 0;
        rx_buf->data_length = 0;
        rx_buf->received_length = 0;
        rx_buf->state = PROTOCOL_STATE_HEADER;
    }
}

//========================================================================
//                              公共函数
//========================================================================

/***************************************************************************
 * 描  述 : 协议初始化
 * 入  参 : 无
 * 返回值 : 无
 **************************************************************************/
void Protocol_Init(void)
{
    Protocol_ResetRxBuffer(&g_uart2_rx_buffer);
    Protocol_ResetRxBuffer(&g_uart3_rx_buffer);

    // 初始化UART2发送数据
    g_uart2_tx_data.shutdown_request = 0;

    // 初始化UART2接收数据
    g_uart2_rx_data.led_mode = LED_MODE_NORMAL;
    g_uart2_rx_data.breath_period = 1;
    g_uart2_rx_data.shutdown_success = 0;
    g_uart2_rx_data.data_updated = 0;

    // 初始化UART3发送数据
    g_uart3_tx_data.shutdown_request = 0;

    // 初始化UART3接收数据
    g_uart3_rx_data.led_mode = LED_MODE_NORMAL;
    g_uart3_rx_data.breath_period = 1;
    g_uart3_rx_data.shutdown_success = 0;
    g_uart3_rx_data.data_updated = 0;
}

/***************************************************************************
 * 描  述 : 计算校验和
 * 入  参 : databuff - 数据指针
 *         length - 数据长度
 * 返回值 : 校验和
 **************************************************************************/
u8 Protocol_CalculateChecksum(u8 *databuff, u8 length)
{
    u8 checksum = 0;
    u8 i;
    
    for(i = 0; i < length; i++)
    {
        checksum += databuff[i];
    }
    
    return (~checksum + 1); // 取反加1
}

/***************************************************************************
 * 描  述 : 发送协议帧
 * 入  参 : uart_num - 串口号(2,3)
 *         frame - 协议帧指针
 * 返回值 : 无
 **************************************************************************/
void Protocol_SendFrame(u8 uart_num, ProtocolFrame_t *frame)
{
    u8 send_buffer[PROTOCOL_MAX_LEN];
    u8 send_length = 0;
    u8 checksum_data[PROTOCOL_DATA_MAX + 1]; // 命令+数据
    u8 i;
    
    // 构建发送数据
    send_buffer[send_length++] = frame->header;
    send_buffer[send_length++] = frame->length;
    send_buffer[send_length++] = frame->command;
    
    // 复制数据
    for(i = 0; i < frame->length - 1; i++) // length包含命令，所以数据长度是length-1
    {
        send_buffer[send_length++] = frame->databuff[i];
    }
    
    // 计算校验和(命令+数据)
    checksum_data[0] = frame->command;
    for(i = 0; i < frame->length - 1; i++)
    {
        checksum_data[i + 1] = frame->databuff[i];
    }
    frame->checksum = Protocol_CalculateChecksum(checksum_data, frame->length);
    
    send_buffer[send_length++] = frame->checksum;
    send_buffer[send_length++] = frame->tail;
    
    // 根据串口号发送数据
    switch(uart_num)
    {
        case 2:
            UART2_SendData(send_buffer, send_length);
            break;
        case 3:
            UART3_SendData(send_buffer, send_length);
            break;
    }
}

/***************************************************************************
 * 描  述 : 发送LED模式设置命令
 * 入  参 : uart_num - 串口号(2,3)
 *         led_mode - LED模式
 * 返回值 : 无
 **************************************************************************/
void Protocol_SendLedMode(u8 uart_num, u8 led_mode)
{
    ProtocolFrame_t frame;
    
    frame.header = PROTOCOL_HEADER;
    frame.length = 2; // 命令(1) + 数据(1)
    frame.command = CMD_LED_MODE;
    frame.databuff[0] = led_mode;
    frame.tail = PROTOCOL_TAIL;
    
    Protocol_SendFrame(uart_num, &frame);
}

/***************************************************************************
 * 描  述 : 发送呼吸灯周期设置命令
 * 入  参 : uart_num - 串口号(2,3)
 *         period_seconds - 呼吸灯周期(秒)
 * 返回值 : 无
 **************************************************************************/
void Protocol_SendBreathPeriod(u8 uart_num, u8 period_seconds)
{
    ProtocolFrame_t frame;

    frame.header = PROTOCOL_HEADER;
    frame.length = 2; // 命令(1) + 数据(1)
    frame.command = CMD_BREATH_PERIOD;
    frame.databuff[0] = period_seconds;
    frame.tail = PROTOCOL_TAIL;

    Protocol_SendFrame(uart_num, &frame);
}

/***************************************************************************
 * 描  述 : 发送关机请求命令
 * 入  参 : uart_num - 串口号(1,2,3)
 * 返回值 : 无
 **************************************************************************/
void Protocol_SendShutdownRequest(u8 uart_num)
{
    ProtocolFrame_t frame;
    
    frame.header = PROTOCOL_HEADER;
    frame.length = 1; // 只有命令
    frame.command = CMD_SHUTDOWN_REQ;
    frame.tail = PROTOCOL_TAIL;
    
    Protocol_SendFrame(uart_num, &frame);
}

/***************************************************************************
 * 描  述 : 发送通用应答
 * 入  参 : uart_num - 串口号(2,3)
 * 返回值 : 无
 **************************************************************************/
void Protocol_SendAck(u8 uart_num)
{
    ProtocolFrame_t frame;

    frame.header = PROTOCOL_HEADER;
    frame.length = 1; // 只有命令
    frame.command = CMD_ACK;
    frame.tail = PROTOCOL_TAIL;

    Protocol_SendFrame(uart_num, &frame);
}

/***************************************************************************
 * 描  述 : 发送关机成功应答
 * 入  参 : uart_num - 串口号(1,2,3)
 * 返回值 : 无
 **************************************************************************/
void Protocol_SendShutdownAck(u8 uart_num)
{
    ProtocolFrame_t frame;

    frame.header = PROTOCOL_HEADER;
    frame.length = 1; // 只有命令
    frame.command = CMD_SHUTDOWN;
    frame.tail = PROTOCOL_TAIL;

    Protocol_SendFrame(uart_num, &frame);
}

/***************************************************************************
 * 描  述 : 处理接收到的数据
 * 入  参 : uart_num - 串口号(1,2,3)
 *         databuff - 接收到的字节
 * 返回值 : 1-接收到完整帧, 0-未接收完整帧
 **************************************************************************/
u8 Protocol_ProcessRxData(u8 uart_num, u8 databuff)
{
    ProtocolRxBuffer_t *rx_buf = Protocol_GetRxBuffer(uart_num);
    if(rx_buf == NULL) return 0;

    switch(rx_buf->state)
    {
        case PROTOCOL_STATE_HEADER:
            if(databuff == PROTOCOL_HEADER)
            {
                rx_buf->buffer[0] = databuff;
                rx_buf->index = 1;
                rx_buf->state = PROTOCOL_STATE_LENGTH;
            }
            break;

        case PROTOCOL_STATE_LENGTH:
            if(databuff > 0 && databuff <= PROTOCOL_DATA_MAX + 1) // 最大数据长度+命令
            {
                rx_buf->buffer[1] = databuff;
                rx_buf->data_length = databuff;
                rx_buf->received_length = 0;
                rx_buf->index = 2;
                rx_buf->state = PROTOCOL_STATE_DATA;
            }
            else
            {
                Protocol_ResetRxBuffer(rx_buf);
            }
            break;

        case PROTOCOL_STATE_DATA:
            rx_buf->buffer[rx_buf->index++] = databuff;
            rx_buf->received_length++;

            if(rx_buf->received_length >= rx_buf->data_length)
            {
                rx_buf->state = PROTOCOL_STATE_CHECKSUM;
            }
            break;

        case PROTOCOL_STATE_CHECKSUM:
            rx_buf->buffer[rx_buf->index++] = databuff;
            rx_buf->state = PROTOCOL_STATE_TAIL;
            break;

        case PROTOCOL_STATE_TAIL:
            if(databuff == PROTOCOL_TAIL)
            {
                rx_buf->buffer[rx_buf->index++] = databuff;
                rx_buf->state = PROTOCOL_STATE_COMPLETE;
                return 1; // 接收完成
            }
            else
            {
                Protocol_ResetRxBuffer(rx_buf);
            }
            break;

        default:
            Protocol_ResetRxBuffer(rx_buf);
            break;
    }

    return 0;
}

/***************************************************************************
 * 描  述 : 处理接收到的命令（三步验证流程）
 * 入  参 : uart_num - 串口号(2,3)
 *         frame - 协议帧指针
 * 返回值 : 无
 **************************************************************************/
void Protocol_HandleCommand(u8 uart_num, ProtocolFrame_t *frame)
{
    ProtocolRxBuffer_t *rx_buf = Protocol_GetRxBuffer(uart_num);
    u8 i;
    u8 checksum_data[PROTOCOL_DATA_MAX + 1];
		u8 calculated_checksum;
		ProtocolRxData_t *rx_data = NULL;

    // 第一步：验证是否为完整帧
    if(rx_buf == NULL || rx_buf->state != PROTOCOL_STATE_COMPLETE)
    {
        return; // 不是完整帧，直接返回
    }

    // 解析接收到的数据
    frame->header = rx_buf->buffer[0];
    frame->length = rx_buf->buffer[1];
    frame->command = rx_buf->buffer[2];

    for(i = 0; i < frame->length - 1; i++) // 数据长度 = length - 1(命令)
    {
        frame->databuff[i] = rx_buf->buffer[3 + i];
    }

    frame->checksum = rx_buf->buffer[2 + frame->length];
    frame->tail = rx_buf->buffer[3 + frame->length];

    // 第二步：验证数据是否正确
    checksum_data[0] = frame->command;
    for(i = 0; i < frame->length - 1; i++)
    {
        checksum_data[i + 1] = frame->databuff[i];
    }

    calculated_checksum = Protocol_CalculateChecksum(checksum_data, frame->length);

    // 调试：记录校验信息
    g_protocol_debug_calculated_checksum = calculated_checksum;
    g_protocol_debug_received_checksum = frame->checksum;

    if(calculated_checksum != frame->checksum)
    {
        // 校验失败，重置缓冲区并返回
        g_protocol_debug_checksum_fail = 1;
        Protocol_ResetRxBuffer(rx_buf);
        return;
    }
    else
    {
        g_protocol_debug_checksum_fail = 0;
    }

    // 第三步：数据正确，赋值到对应UART的接收数据，并发送应答
    rx_data = Protocol_GetRxData(uart_num);
    if(rx_data == NULL)
    {
        Protocol_ResetRxBuffer(rx_buf);
        return;
    }

    switch(frame->command)
    {
        case CMD_LED_MODE:  // 接收到LED模式设置
            if(frame->length >= 2)
            {
                rx_data->led_mode = frame->databuff[0];
                rx_data->data_updated = 1;  // 标记数据已更新

                // 自动发送通用应答帧
                Protocol_SendAck(uart_num);
            }
            break;

        case CMD_BREATH_PERIOD:  // 接收到呼吸灯周期设置
            if(frame->length >= 2)
            {
                rx_data->breath_period = frame->databuff[0];  // 现在是秒数
                rx_data->data_updated = 1;  // 标记数据已更新

                // 自动发送通用应答帧
                Protocol_SendAck(uart_num);
            }
            break;

        case CMD_SHUTDOWN:  // 接收到关机成功消息
            rx_data->shutdown_success = 1;  // 设置关机成功标志
            rx_data->data_updated = 1;      // 标记数据已更新

            // 自动发送通用应答帧
            Protocol_SendAck(uart_num);
            break;

        case CMD_ACK:  // 接收到通用应答
            // 对方确认收到了我们发送的关机请求
            // 不需要再次应答（应答的应答）
            break;
    }

    // 重置接收缓冲区
    Protocol_ResetRxBuffer(rx_buf);
}

/***************************************************************************
 * 描  述 : 中断中调用的协议处理函数（直接处理完整帧）
 * 入  参 : uart_num - 串口号(2,3)
 *         databuff - 接收到的字节
 * 返回值 : 无
 **************************************************************************/
void Protocol_ProcessRxData_ISR(u8 uart_num, u8 databuff)
{
    if(Protocol_ProcessRxData(uart_num, databuff))
    {
        // 接收到完整帧，直接在中断中处理
        ProtocolFrame_t frame;
        g_protocol_debug_frame_count++;  // 完整帧计数

        Protocol_HandleCommand(uart_num, &frame);
        g_protocol_debug_process_count++; // 处理计数
    }
}

/***************************************************************************
 * 描  述 : 检查并清除指定UART的接收数据更新标志
 * 入  参 : uart_num - 串口号(2,3)
 * 返回值 : 1-有新数据, 0-无新数据
 **************************************************************************/
u8 Protocol_CheckDataUpdated(u8 uart_num)
{
    ProtocolRxData_t *rx_data = Protocol_GetRxData(uart_num);
    if(rx_data == NULL) return 0;

    if(rx_data->data_updated)
    {
        rx_data->data_updated = 0;  // 清除标志
        return 1;  // 有新数据
    }
    return 0;  // 无新数据
}


