#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制器测试
测试电源控制器的各种功能
"""

import unittest
import sys
import os
import time
from unittest.mock import Mock, patch, MagicMock

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))

from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod, PowerCommand


class TestPowerControllerBasics(unittest.TestCase):
    """电源控制器基础功能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.controller = N100PowerController(
            port='/dev/test_port',
            use_manager=False,  # 禁用串口管理器
            use_proxy=False     # 禁用代理模式
        )
    
    def test_controller_initialization(self):
        """测试控制器初始化"""
        self.assertEqual(self.controller.port, '/dev/test_port')
        self.assertEqual(self.controller.baudrate, 115200)
        self.assertEqual(self.controller.timeout, 1.0)
        self.assertEqual(self.controller.max_retries, 10)
        self.assertFalse(self.controller.is_connected)
    
    def test_frame_creation(self):
        """测试帧创建"""
        # 测试LED模式帧
        led_normal_frame = self.controller._create_frame(PowerCommand.LED_MODE, bytes([LEDMode.NORMAL]))
        expected = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
        self.assertEqual(led_normal_frame, expected)
        
        # 测试呼吸周期帧
        breath_3s_frame = self.controller._create_frame(PowerCommand.BREATH_PERIOD, bytes([BreathPeriod.PERIOD_3S]))
        expected = bytes([0xAA, 0x02, 0x02, 0x03, 0xFB, 0x55])
        self.assertEqual(breath_3s_frame, expected)
        
        # 测试关机成功帧
        shutdown_frame = self.controller._create_frame(PowerCommand.SHUTDOWN)
        expected = bytes([0xAA, 0x01, 0x03, 0xFD, 0x55])
        self.assertEqual(shutdown_frame, expected)
    
    def test_checksum_calculation(self):
        """测试校验和计算"""
        test_cases = [
            (bytes([0x01, 0x00]), 0xFF),  # LED正常模式
            (bytes([0x01, 0x01]), 0xFE),  # LED呼吸模式
            (bytes([0x02, 0x03]), 0xFB),  # 3秒呼吸周期
            (bytes([0x03]), 0xFD),        # 关机成功
        ]
        
        for data, expected in test_cases:
            result = self.controller._calculate_checksum(data)
            self.assertEqual(result, expected, 
                f"校验和计算错误: 数据={data.hex()}, 期望=0x{expected:02X}, 实际=0x{result:02X}")


class TestPowerControllerCallbacks(unittest.TestCase):
    """电源控制器回调功能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.controller = N100PowerController(port='/dev/test_port', use_manager=False, use_proxy=False)
        self.ack_callback_called = False
        self.error_callback_called = False
        self.last_ack_command = None
        self.last_error_message = None
    
    def ack_callback(self, command):
        """ACK回调函数"""
        self.ack_callback_called = True
        self.last_ack_command = command
    
    def error_callback(self, error_msg):
        """错误回调函数"""
        self.error_callback_called = True
        self.last_error_message = error_msg
    
    def test_callback_setting(self):
        """测试回调函数设置"""
        self.controller.set_ack_callback(self.ack_callback)
        self.controller.set_error_callback(self.error_callback)
        
        self.assertEqual(self.controller.on_ack_received, self.ack_callback)
        self.assertEqual(self.controller.on_error, self.error_callback)
    
    @patch('serial.Serial')
    def test_ack_callback_execution(self, mock_serial):
        """测试ACK回调执行"""
        # 模拟串口
        mock_serial_instance = MagicMock()
        mock_serial.return_value = mock_serial_instance
        mock_serial_instance.is_open = True
        mock_serial_instance.in_waiting = 5
        mock_serial_instance.read.return_value = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])  # ACK帧
        
        # 设置回调
        self.controller.set_ack_callback(self.ack_callback)
        
        # 连接并发送命令
        self.controller.connect()
        self.controller.set_led_normal()
        
        # 验证回调被调用
        # 注意：由于是模拟测试，实际的ACK处理可能需要更复杂的模拟


class TestPowerControllerCommands(unittest.TestCase):
    """电源控制器命令测试"""
    
    def setUp(self):
        """测试前准备"""
        self.controller = N100PowerController(port='/dev/test_port', use_manager=False, use_proxy=False)
    
    def test_led_mode_commands(self):
        """测试LED模式命令"""
        # 测试便捷方法
        self.assertTrue(hasattr(self.controller, 'set_led_normal'))
        self.assertTrue(hasattr(self.controller, 'set_led_breath'))
        
        # 测试通用方法
        self.assertTrue(hasattr(self.controller, 'set_led_mode'))
    
    def test_breath_period_commands(self):
        """测试呼吸周期命令"""
        # 测试便捷方法
        self.assertTrue(hasattr(self.controller, 'set_breath_1s'))
        self.assertTrue(hasattr(self.controller, 'set_breath_3s'))
        self.assertTrue(hasattr(self.controller, 'set_breath_5s'))
        
        # 测试通用方法
        self.assertTrue(hasattr(self.controller, 'set_breath_period'))
    
    def test_shutdown_command(self):
        """测试关机命令"""
        self.assertTrue(hasattr(self.controller, 'send_shutdown_success'))
    
    def test_custom_command(self):
        """测试自定义命令"""
        self.assertTrue(hasattr(self.controller, 'send_custom_command'))


class TestPowerControllerStatus(unittest.TestCase):
    """电源控制器状态测试"""
    
    def setUp(self):
        """测试前准备"""
        self.controller = N100PowerController(port='/dev/test_port', use_manager=False, use_proxy=False)
    
    def test_initial_status(self):
        """测试初始状态"""
        self.assertFalse(self.controller.is_connected)
        self.assertFalse(self.controller.is_port_connected())
    
    def test_port_info(self):
        """测试端口信息"""
        info = self.controller.get_port_info()
        self.assertIsInstance(info, dict)
        self.assertIn('port', info)
        self.assertIn('baudrate', info)
        self.assertIn('timeout', info)
        self.assertIn('connected', info)
    
    @patch('serial.Serial')
    def test_connection_status(self, mock_serial):
        """测试连接状态"""
        # 模拟成功连接
        mock_serial_instance = MagicMock()
        mock_serial.return_value = mock_serial_instance
        mock_serial_instance.is_open = True
        
        # 连接
        result = self.controller.connect()
        self.assertTrue(result)
        self.assertTrue(self.controller.is_connected)
        
        # 断开连接
        self.controller.disconnect()
        self.assertFalse(self.controller.is_connected)


class TestPowerControllerErrorHandling(unittest.TestCase):
    """电源控制器错误处理测试"""
    
    def setUp(self):
        """测试前准备"""
        self.controller = N100PowerController(port='/dev/test_port', use_manager=False, use_proxy=False)
    
    def test_disconnected_operations(self):
        """测试未连接状态下的操作"""
        # 未连接时所有操作都应该返回False
        self.assertFalse(self.controller.set_led_normal())
        self.assertFalse(self.controller.set_led_breath())
        self.assertFalse(self.controller.set_breath_1s())
        self.assertFalse(self.controller.send_shutdown_success())
        self.assertFalse(self.controller.send_custom_command(0x01, b'\x00'))
    
    @patch('serial.Serial')
    def test_connection_failure(self, mock_serial):
        """测试连接失败"""
        # 模拟连接失败
        mock_serial.side_effect = Exception("Connection failed")
        
        result = self.controller.connect()
        self.assertFalse(result)
        self.assertFalse(self.controller.is_connected)
    
    def test_invalid_parameters(self):
        """测试无效参数"""
        # 测试无效的LED模式
        with self.assertRaises((ValueError, TypeError)):
            self.controller.set_led_mode(999)
        
        # 测试无效的呼吸周期
        with self.assertRaises((ValueError, TypeError)):
            self.controller.set_breath_period(999)


class TestPowerControllerIntegration(unittest.TestCase):
    """电源控制器集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.controller = N100PowerController(port='/dev/test_port', use_manager=False, use_proxy=False)
    
    @patch('serial.Serial')
    def test_complete_workflow(self, mock_serial):
        """测试完整工作流程"""
        # 模拟串口
        mock_serial_instance = MagicMock()
        mock_serial.return_value = mock_serial_instance
        mock_serial_instance.is_open = True
        mock_serial_instance.in_waiting = 0
        mock_serial_instance.read.return_value = b''
        
        # 设置回调
        ack_count = 0
        error_count = 0
        
        def ack_callback(command):
            nonlocal ack_count
            ack_count += 1
        
        def error_callback(error_msg):
            nonlocal error_count
            error_count += 1
        
        self.controller.set_ack_callback(ack_callback)
        self.controller.set_error_callback(error_callback)
        
        # 执行完整流程
        self.assertTrue(self.controller.connect())
        
        # 执行各种命令
        commands = [
            self.controller.set_led_normal,
            self.controller.set_led_breath,
            self.controller.set_breath_1s,
            self.controller.set_breath_3s,
            self.controller.set_breath_5s,
            self.controller.send_shutdown_success,
        ]
        
        for cmd in commands:
            # 由于是模拟测试，命令可能会超时，但不应该抛出异常
            try:
                cmd()
            except Exception as e:
                self.fail(f"命令执行抛出异常: {e}")
        
        # 断开连接
        self.controller.disconnect()
        self.assertFalse(self.controller.is_connected)


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestPowerControllerBasics,
        TestPowerControllerCallbacks,
        TestPowerControllerCommands,
        TestPowerControllerStatus,
        TestPowerControllerErrorHandling,
        TestPowerControllerIntegration,
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    print("N100电源控制器测试")
    print("=" * 50)
    
    success = run_tests()
    
    if success:
        print("\n🎉 所有电源控制器测试通过！")
        exit(0)
    else:
        print("\n❌ 部分电源控制器测试失败！")
        exit(1)
