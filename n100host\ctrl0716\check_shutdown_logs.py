#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查关机守护进程日志
分析关机请求处理情况
"""

import subprocess
import sys
import re
from datetime import datetime, timedelta


def get_recent_logs(service_name, minutes=10):
    """获取最近的日志"""
    try:
        since_time = datetime.now() - timedelta(minutes=minutes)
        since_str = since_time.strftime("%Y-%m-%d %H:%M:%S")
        
        result = subprocess.run([
            'journalctl', '-u', service_name, 
            '--since', since_str,
            '--no-pager'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            return result.stdout
        else:
            print(f"获取 {service_name} 日志失败: {result.stderr}")
            return ""
    
    except Exception as e:
        print(f"获取日志异常: {e}")
        return ""


def analyze_shutdown_logs():
    """分析关机守护进程日志"""
    print("=== 关机守护进程日志分析 ===")
    
    # 获取最近10分钟的日志
    logs = get_recent_logs('n100-shutdown-proxy', 10)
    
    if not logs:
        print("❌ 无法获取日志")
        return
    
    lines = logs.split('\n')
    
    # 分析关键事件
    events = {
        'startup': [],
        'connection': [],
        'shutdown_request': [],
        'ack_sent': [],
        'shutdown_success': [],
        'errors': [],
        'timeouts': []
    }
    
    for line in lines:
        if not line.strip():
            continue
        
        # 启动事件
        if '启动N100关机守护进程' in line:
            events['startup'].append(line)
        
        # 连接事件
        elif '成功连接到串口代理服务' in line:
            events['connection'].append(line)
        
        # 关机请求
        elif '收到电源板关机请求' in line or '关机请求' in line:
            events['shutdown_request'].append(line)
        
        # ACK发送
        elif 'ACK应答' in line:
            events['ack_sent'].append(line)
        
        # 关机成功
        elif '关机成功' in line:
            events['shutdown_success'].append(line)
        
        # 超时
        elif 'timed out' in line:
            events['timeouts'].append(line)
        
        # 错误
        elif 'ERROR' in line:
            events['errors'].append(line)
    
    # 输出分析结果
    print(f"\n📊 事件统计 (最近10分钟):")
    print(f"  启动事件: {len(events['startup'])}")
    print(f"  连接事件: {len(events['connection'])}")
    print(f"  关机请求: {len(events['shutdown_request'])}")
    print(f"  ACK发送: {len(events['ack_sent'])}")
    print(f"  关机成功: {len(events['shutdown_success'])}")
    print(f"  超时事件: {len(events['timeouts'])}")
    print(f"  错误事件: {len(events['errors'])}")
    
    # 详细显示关键事件
    if events['shutdown_request']:
        print(f"\n🔍 关机请求详情:")
        for event in events['shutdown_request']:
            print(f"  {event}")
    else:
        print(f"\n⚠️  未发现关机请求处理记录")
        print(f"  这可能表明关机守护进程没有接收到关机请求")
    
    if events['shutdown_success']:
        print(f"\n✅ 关机成功事件:")
        for event in events['shutdown_success']:
            print(f"  {event}")
    
    if events['errors']:
        print(f"\n❌ 错误事件:")
        for event in events['errors'][-5:]:  # 只显示最近5个错误
            print(f"  {event}")
    
    if events['timeouts']:
        print(f"\n⏰ 超时事件 (最近3个):")
        for event in events['timeouts'][-3:]:
            print(f"  {event}")
        print(f"  注意: 超时通常是正常的，表示在等待消息")


def analyze_proxy_logs():
    """分析串口代理日志"""
    print(f"\n=== 串口代理日志分析 ===")
    
    logs = get_recent_logs('n100-serial-proxy', 10)
    
    if not logs:
        print("❌ 无法获取串口代理日志")
        return
    
    lines = logs.split('\n')
    
    # 查找关机相关的处理
    shutdown_related = []
    ack_sent = []
    
    for line in lines:
        if not line.strip():
            continue
        
        # 查找关机请求处理
        if '0x13' in line or '关机' in line:
            shutdown_related.append(line)
        
        # 查找ACK发送
        if 'ACK' in line and '发送' in line:
            ack_sent.append(line)
    
    print(f"📊 串口代理事件:")
    print(f"  关机相关: {len(shutdown_related)}")
    print(f"  ACK发送: {len(ack_sent)}")
    
    if shutdown_related:
        print(f"\n🔍 关机相关事件:")
        for event in shutdown_related:
            print(f"  {event}")
    
    if ack_sent:
        print(f"\n📤 ACK发送事件:")
        for event in ack_sent[-3:]:  # 最近3个
            print(f"  {event}")


def main():
    """主函数"""
    print("N100关机守护进程日志分析")
    print("=" * 50)
    
    try:
        analyze_shutdown_logs()
        analyze_proxy_logs()
        
        print(f"\n" + "=" * 50)
        print("📋 分析总结:")
        print("1. 如果看到'关机请求'记录，说明关机守护进程正确接收了消息")
        print("2. 如果看到'ACK发送'记录，说明串口代理正确发送了ACK")
        print("3. 超时错误通常是正常的，表示在等待新消息")
        print("4. 如果没有关机请求记录，可能是消息过滤或转发问题")
        
        print(f"\n💡 建议:")
        print("- 如果没有关机请求记录，检查消息过滤器设置")
        print("- 如果有关机请求但没有关机成功，检查关机脚本")
        print("- 可以尝试手动发送关机请求进行测试")
    
    except KeyboardInterrupt:
        print(f"\n⚠️  分析被中断")
    
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")


if __name__ == "__main__":
    main()
