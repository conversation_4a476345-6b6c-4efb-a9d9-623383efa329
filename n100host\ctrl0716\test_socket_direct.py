#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接测试Unix Socket连接
不使用我们的客户端类，直接测试原始Socket连接
"""

import socket
import time
import json


def test_raw_socket_connection():
    """测试原始Socket连接"""
    print("=== 原始Socket连接测试 ===")
    
    socket_path = "/tmp/n100_serial_proxy.sock"
    
    try:
        print(f"尝试连接到: {socket_path}")
        
        # 创建Unix Socket
        sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        
        # 设置超时
        sock.settimeout(5.0)
        
        print("正在连接...")
        sock.connect(socket_path)
        print("✅ 连接成功！")
        
        # 发送注册消息
        register_msg = {
            "msg_type": "register",
            "client_id": "raw_test",
            "data": ""
        }
        
        message = json.dumps(register_msg) + '\n'
        print(f"发送注册消息: {message.strip()}")
        
        sock.send(message.encode('utf-8'))
        print("✅ 注册消息已发送")
        
        # 等待响应
        print("等待响应...")
        sock.settimeout(3.0)
        
        response = sock.recv(1024)
        print(f"✅ 收到响应: {response.decode('utf-8').strip()}")
        
        # 发送关机请求
        shutdown_msg = {
            "msg_type": "send_request",
            "client_id": "raw_test",
            "data": "AA0113ED55"  # 关机请求的十六进制字符串
        }
        
        message = json.dumps(shutdown_msg) + '\n'
        print(f"发送关机请求: {message.strip()}")
        
        sock.send(message.encode('utf-8'))
        print("✅ 关机请求已发送")
        
        # 等待更多响应
        print("等待更多响应...")
        time.sleep(2)
        
        try:
            response = sock.recv(1024)
            if response:
                print(f"✅ 收到额外响应: {response.decode('utf-8').strip()}")
            else:
                print("⚠️  没有额外响应")
        except socket.timeout:
            print("⚠️  等待响应超时（这是正常的）")
        
        sock.close()
        print("✅ 连接已关闭")
        
        return True
        
    except socket.timeout:
        print("❌ 连接超时")
        return False
    
    except ConnectionRefusedError:
        print("❌ 连接被拒绝")
        return False
    
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False


def test_socket_with_retry():
    """带重试的Socket测试"""
    print("\n=== 带重试的Socket测试 ===")
    
    for attempt in range(3):
        print(f"\n尝试 {attempt + 1}/3:")
        
        if test_raw_socket_connection():
            print("✅ 测试成功")
            return True
        
        if attempt < 2:
            print("等待2秒后重试...")
            time.sleep(2)
    
    print("❌ 所有尝试都失败了")
    return False


def check_socket_file():
    """检查Socket文件状态"""
    print("\n=== Socket文件检查 ===")
    
    import os
    import stat
    
    socket_path = "/tmp/n100_serial_proxy.sock"
    
    if os.path.exists(socket_path):
        print(f"✅ Socket文件存在: {socket_path}")
        
        # 获取文件状态
        file_stat = os.stat(socket_path)
        
        print(f"  文件类型: {stat.filemode(file_stat.st_mode)}")
        print(f"  权限: {oct(file_stat.st_mode)[-3:]}")
        print(f"  所有者: UID={file_stat.st_uid}, GID={file_stat.st_gid}")
        print(f"  大小: {file_stat.st_size} 字节")
        print(f"  修改时间: {time.ctime(file_stat.st_mtime)}")
        
        # 检查是否是Socket文件
        if stat.S_ISSOCK(file_stat.st_mode):
            print("✅ 确认是Socket文件")
        else:
            print("❌ 不是Socket文件")
            
    else:
        print(f"❌ Socket文件不存在: {socket_path}")


def main():
    """主函数"""
    print("Unix Socket直接连接测试")
    print("=" * 40)
    
    check_socket_file()
    
    success = test_socket_with_retry()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 Socket连接测试成功！")
        print("这说明串口代理的Socket服务器工作正常。")
        print("问题可能在于我们的客户端代码。")
    else:
        print("❌ Socket连接测试失败！")
        print("这说明串口代理的Socket服务器有问题。")
        print("建议重启串口代理服务：")
        print("  sudo systemctl restart n100-serial-proxy")


if __name__ == "__main__":
    main()
