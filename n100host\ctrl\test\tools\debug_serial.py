#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
串口调试脚本
详细调试串口通信问题
"""

import os
import sys
import time
import serial

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from n100_power_ctrl import N100PowerController, LEDMode
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    sys.exit(1)


def check_serial_device(port='/dev/ttyS4'):
    """检查串口设备"""
    print(f"=== 检查串口设备 {port} ===")
    
    # 检查设备是否存在
    if not os.path.exists(port):
        print(f"❌ 串口设备 {port} 不存在")
        return False
    
    print(f"✅ 串口设备 {port} 存在")
    
    # 检查权限
    if os.access(port, os.R_OK | os.W_OK):
        print(f"✅ 串口设备权限正常")
    else:
        print(f"❌ 串口设备权限不足")
        print(f"   请运行: sudo chmod 666 {port}")
        return False
    
    return True


def test_raw_serial(port='/dev/ttyS4'):
    """测试原始串口通信"""
    print(f"\n=== 测试原始串口通信 ===")
    
    try:
        # 打开串口
        ser = serial.Serial(port, 115200, timeout=2)
        print(f"✅ 串口打开成功")
        print(f"   端口: {ser.port}")
        print(f"   波特率: {ser.baudrate}")
        print(f"   超时: {ser.timeout}")
        print(f"   是否打开: {ser.is_open}")
        
        # 清空缓冲区
        ser.reset_input_buffer()
        ser.reset_output_buffer()
        print(f"✅ 缓冲区已清空")
        
        # 发送LED正常模式命令
        led_frame = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
        print(f"\n[发送] LED正常模式帧: {led_frame.hex(' ').upper()}")
        
        ser.write(led_frame)
        ser.flush()
        print(f"[发送完成] 已发送 {len(led_frame)} 字节")
        
        # 等待应答
        print(f"[等待] 等待应答，超时2秒...")
        start_time = time.time()
        
        while time.time() - start_time < 2.0:
            if ser.in_waiting > 0:
                response = ser.read(ser.in_waiting)
                print(f"[接收] 应答数据: {response.hex(' ').upper()}")
                
                # 检查是否为ACK
                expected_ack = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
                if response == expected_ack:
                    print(f"✅ 收到正确的ACK应答")
                    ser.close()
                    return True
                else:
                    print(f"⚠️ 应答不匹配，期望: {expected_ack.hex(' ').upper()}")
            
            time.sleep(0.1)
        
        print(f"❌ 2秒内未收到应答")
        ser.close()
        return False
        
    except Exception as e:
        print(f"❌ 原始串口测试失败: {e}")
        return False


def test_power_controller(port='/dev/ttyS4'):
    """测试电源控制器"""
    print(f"\n=== 测试电源控制器 ===")
    
    try:
        # 创建控制器（不使用串口管理器）
        controller = N100PowerController(
            port=port,
            use_manager=False,
            max_retries=1,  # 只试一次，便于调试
            timeout=3.0
        )
        
        print(f"控制器配置:")
        print(f"   端口: {controller.port}")
        print(f"   使用管理器: {controller.use_manager}")
        print(f"   最大重试: {controller.max_retries}")
        print(f"   超时: {controller.timeout}")
        
        # 连接
        print(f"\n[连接] 尝试连接串口...")
        if controller.connect():
            print(f"✅ 控制器连接成功")
            
            # 测试LED命令
            print(f"\n[测试] 发送LED正常模式命令...")
            success = controller.set_led_mode(LEDMode.NORMAL)
            print(f"[结果] LED命令: {'✅ 成功' if success else '❌ 失败'}")
            
            controller.disconnect()
            return success
        else:
            print(f"❌ 控制器连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 电源控制器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cli_command():
    """测试CLI命令"""
    print(f"\n=== 测试CLI命令 ===")
    
    try:
        import subprocess
        
        # 测试CLI命令
        cmd = [sys.executable, 'power_ctrl_cli.py', '--no-manager', '--retries', '1', 'led', 'normal']
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        print(f"返回码: {result.returncode}")
        print(f"标准输出:")
        print(result.stdout)
        
        if result.stderr:
            print(f"标准错误:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ CLI命令测试失败: {e}")
        return False


def main():
    """主函数"""
    print("N100串口详细调试")
    print("=" * 40)
    
    port = '/dev/ttyS4'
    
    # 1. 检查串口设备
    device_ok = check_serial_device(port)
    
    # 2. 测试原始串口
    raw_ok = False
    if device_ok:
        raw_ok = test_raw_serial(port)
    
    # 3. 测试电源控制器
    controller_ok = False
    if device_ok:
        controller_ok = test_power_controller(port)
    
    # 4. 测试CLI命令
    cli_ok = False
    if device_ok:
        cli_ok = test_cli_command()
    
    # 显示结果
    print(f"\n=== 调试结果总结 ===")
    print(f"串口设备检查: {'✅ 正常' if device_ok else '❌ 异常'}")
    print(f"原始串口通信: {'✅ 正常' if raw_ok else '❌ 异常'}")
    print(f"电源控制器: {'✅ 正常' if controller_ok else '❌ 异常'}")
    print(f"CLI命令: {'✅ 正常' if cli_ok else '❌ 异常'}")
    
    # 诊断建议
    print(f"\n=== 诊断建议 ===")
    if not device_ok:
        print("1. 检查硬件连接")
        print("2. 确认串口设备路径正确")
        print("3. 检查串口权限")
    elif not raw_ok:
        print("1. 检查电源板是否上电")
        print("2. 确认电源板固件支持当前协议")
        print("3. 检查串口连接线")
    elif not controller_ok:
        print("1. 检查电源控制器代码")
        print("2. 确认ACK解析逻辑")
    elif not cli_ok:
        print("1. 检查CLI参数解析")
        print("2. 确认模块导入")
    else:
        print("✅ 所有测试都通过，系统正常工作")
    
    return 0 if all([device_ok, raw_ok, controller_ok, cli_ok]) else 1


if __name__ == "__main__":
    sys.exit(main())
