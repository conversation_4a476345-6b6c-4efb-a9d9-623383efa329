#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终诊断脚本
确定串口通信问题的根本原因
"""

import os
import sys
import time
import serial

def check_serial_ports():
    """检查可用的串口"""
    print("=== 检查可用串口 ===")
    
    # Linux常见串口设备
    possible_ports = [
        '/dev/ttyS0', '/dev/ttyS1', '/dev/ttyS2', '/dev/ttyS3', '/dev/ttyS4',
        '/dev/ttyUSB0', '/dev/ttyUSB1', '/dev/ttyUSB2',
        '/dev/ttyACM0', '/dev/ttyACM1', '/dev/ttyACM2'
    ]
    
    available_ports = []
    
    for port in possible_ports:
        if os.path.exists(port):
            try:
                # 尝试打开串口
                ser = serial.Serial(port, 115200, timeout=0.1)
                ser.close()
                available_ports.append(port)
                print(f"✅ {port} - 可用")
            except Exception as e:
                print(f"❌ {port} - 无法打开: {e}")
        else:
            print(f"⚪ {port} - 不存在")
    
    return available_ports

def test_port_communication(port):
    """测试指定端口的通信"""
    print(f"\n=== 测试端口 {port} ===")
    
    try:
        # 打开串口
        ser = serial.Serial(port, 115200, timeout=2)
        print(f"✅ 端口 {port} 打开成功")
        
        # 发送测试帧
        test_frame = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
        print(f"[发送] 测试帧: {test_frame.hex(' ').upper()}")
        
        # 清空缓冲区
        ser.reset_input_buffer()
        ser.reset_output_buffer()
        
        # 发送数据
        ser.write(test_frame)
        ser.flush()
        print(f"[发送完成] 已发送 {len(test_frame)} 字节")
        
        # 等待应答
        print(f"[等待] 等待应答，超时2秒...")
        start_time = time.time()
        received_data = b''
        
        while time.time() - start_time < 2.0:
            if ser.in_waiting > 0:
                data = ser.read(ser.in_waiting)
                received_data += data
                print(f"[接收] 数据: {data.hex(' ').upper()}")
            time.sleep(0.1)
        
        if received_data:
            print(f"[总计] 接收到 {len(received_data)} 字节: {received_data.hex(' ').upper()}")
            
            # 检查是否为期望的ACK
            expected_ack = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
            if expected_ack in received_data:
                print(f"✅ 收到正确的ACK应答")
                ser.close()
                return True
            else:
                print(f"⚠️ 收到数据但不是期望的ACK")
                print(f"   期望: {expected_ack.hex(' ').upper()}")
        else:
            print(f"❌ 未收到任何应答")
        
        ser.close()
        return False
        
    except Exception as e:
        print(f"❌ 端口 {port} 测试失败: {e}")
        return False

def test_loopback(port):
    """测试串口回环"""
    print(f"\n=== 测试端口 {port} 回环 ===")
    
    try:
        ser = serial.Serial(port, 115200, timeout=1)
        
        # 发送测试数据
        test_data = b"HELLO"
        print(f"[发送] 测试数据: {test_data}")
        
        ser.write(test_data)
        ser.flush()
        
        # 尝试读取（如果有回环）
        time.sleep(0.5)
        if ser.in_waiting > 0:
            received = ser.read(ser.in_waiting)
            print(f"[接收] 回环数据: {received}")
            if received == test_data:
                print(f"✅ 检测到完整回环")
            else:
                print(f"⚠️ 检测到部分回环")
        else:
            print(f"✅ 无回环（正常）")
        
        ser.close()
        return True
        
    except Exception as e:
        print(f"❌ 回环测试失败: {e}")
        return False

def main():
    """主函数"""
    print("N100串口最终诊断")
    print("=" * 40)
    
    # 1. 检查可用串口
    available_ports = check_serial_ports()
    
    if not available_ports:
        print("\n❌ 没有找到可用的串口设备")
        print("建议:")
        print("1. 检查硬件连接")
        print("2. 确认串口设备是否正确连接")
        print("3. 检查设备权限")
        return 1
    
    print(f"\n✅ 找到 {len(available_ports)} 个可用串口")
    
    # 2. 测试每个可用端口
    working_ports = []
    
    for port in available_ports:
        # 测试通信
        if test_port_communication(port):
            working_ports.append(port)
        
        # 测试回环
        test_loopback(port)
    
    # 3. 显示结果
    print(f"\n=== 诊断结果 ===")
    print(f"可用串口: {len(available_ports)} 个")
    print(f"响应串口: {len(working_ports)} 个")
    
    if working_ports:
        print(f"\n✅ 以下串口有响应:")
        for port in working_ports:
            print(f"   {port}")
        
        print(f"\n建议使用命令:")
        for port in working_ports:
            print(f"   python3 power_ctrl_cli.py --port {port} --no-manager led normal")
    
    elif available_ports:
        print(f"\n⚠️ 串口可用但无响应，可能原因:")
        print("1. 电源板未上电")
        print("2. 电源板固件不支持当前协议")
        print("3. 串口连接线问题")
        print("4. 波特率不匹配")
        
        print(f"\n建议测试命令:")
        for port in available_ports:
            print(f"   python3 power_ctrl_cli.py --port {port} --no-manager led normal")
    
    else:
        print(f"\n❌ 没有可用的串口设备")
    
    return 0 if working_ports else 1

if __name__ == "__main__":
    sys.exit(main())
