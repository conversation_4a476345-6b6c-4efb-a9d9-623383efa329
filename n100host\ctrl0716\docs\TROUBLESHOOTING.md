# N100电源控制系统故障排除指南

## 概述

本文档提供了N100电源控制系统常见问题的诊断和解决方法，帮助用户快速定位和解决系统故障。

## 快速诊断

### 系统健康检查

运行以下命令进行快速系统检查：

```bash
# 运行系统测试脚本
./scripts/test_system.sh

# 检查服务状态
systemctl status n100-serial-proxy n100-shutdown-proxy

# 检查串口设备
ls -la /dev/ttyS4

# 检查进程
ps aux | grep n100
```

### 日志检查

```bash
# 查看最新日志
journalctl -u n100-serial-proxy -n 20
journalctl -u n100-shutdown-proxy -n 20

# 实时监控日志
journalctl -u n100-serial-proxy -f
```

## 常见问题分类

### 1. 连接问题

#### 问题1.1: 串口设备不存在

**症状**:
- 错误信息: "No such file or directory: '/dev/ttyS4'"
- 无法打开串口设备

**诊断**:
```bash
# 检查串口设备
ls -la /dev/ttyS*
dmesg | grep ttyS
```

**解决方法**:
```bash
# 检查硬件连接
# 确认串口设备路径
# 如果使用USB转串口，检查USB设备
lsusb
dmesg | grep USB

# 更新设备路径
# 修改配置文件中的串口路径
```

#### 问题1.2: 串口权限不足

**症状**:
- 错误信息: "Permission denied"
- 普通用户无法访问串口

**诊断**:
```bash
# 检查串口权限
ls -la /dev/ttyS4

# 检查用户组
groups $USER
```

**解决方法**:
```bash
# 方法1: 添加用户到dialout组
sudo usermod -a -G dialout $USER
# 重新登录生效

# 方法2: 直接修改权限
sudo chmod 666 /dev/ttyS4

# 方法3: 创建udev规则
echo 'KERNEL=="ttyS4", MODE="0666"' | sudo tee /etc/udev/rules.d/99-n100-serial.rules
sudo udevadm control --reload-rules
```

#### 问题1.3: 串口被占用

**症状**:
- 错误信息: "Device or resource busy"
- 多个进程尝试访问同一串口

**诊断**:
```bash
# 检查串口占用
lsof /dev/ttyS4
fuser /dev/ttyS4
```

**解决方法**:
```bash
# 终止占用进程
sudo fuser -k /dev/ttyS4

# 或者使用代理架构避免冲突
sudo ./scripts/install_proxy_system.sh
```

### 2. 服务问题

#### 问题2.1: 服务启动失败

**症状**:
- systemctl status显示failed状态
- 服务无法正常启动

**诊断**:
```bash
# 查看服务状态
systemctl status n100-serial-proxy -l

# 查看启动日志
journalctl -u n100-serial-proxy --since "10 minutes ago"

# 检查服务配置
systemctl cat n100-serial-proxy
```

**解决方法**:
```bash
# 检查Python路径
which python3

# 检查文件权限
ls -la /opt/n100/ctrl/

# 修复权限
sudo chmod +x /opt/n100/ctrl/*.py

# 重新加载配置
sudo systemctl daemon-reload
sudo systemctl restart n100-serial-proxy
```

#### 问题2.2: 服务频繁重启

**症状**:
- 服务状态显示重启次数过多
- 日志中出现重复的启动信息

**诊断**:
```bash
# 查看重启历史
systemctl status n100-serial-proxy
journalctl -u n100-serial-proxy | grep "Started\|Stopped"
```

**解决方法**:
```bash
# 检查依赖问题
pip3 list | grep serial

# 增加重启延迟
sudo systemctl edit n100-serial-proxy
# 添加: RestartSec=10

# 检查资源限制
# 修改服务配置增加内存限制
```

### 3. 通信问题

#### 问题3.1: 命令超时

**症状**:
- 命令执行超时
- 未收到ACK应答

**诊断**:
```bash
# 测试串口通信
echo -ne '\xAA\x02\x01\x00\xFF\x55' | sudo tee /dev/ttyS4

# 监控串口数据
sudo cat /dev/ttyS4 | hexdump -C

# 检查电源板状态
```

**解决方法**:
```bash
# 增加超时时间
# 修改源代码中的timeout参数

# 检查串口配置
stty -F /dev/ttyS4 115200 cs8 -cstopb -parity

# 检查硬件连接
# 确认电源板正常工作
```

#### 问题3.2: 数据校验失败

**症状**:
- 接收到无效帧
- 校验和错误

**诊断**:
```bash
# 启用详细日志
# 修改源代码增加调试输出

# 监控原始数据
sudo strace -e read,write -p $(pgrep -f "serial_proxy_daemon")
```

**解决方法**:
```bash
# 检查通信质量
# 降低波特率测试
# 检查线缆质量
# 添加通信重试机制
```

### 4. 代理问题

#### 问题4.1: 代理连接失败

**症状**:
- 客户端无法连接到代理
- Socket连接超时

**诊断**:
```bash
# 检查Socket文件
ls -la /tmp/n100_serial_proxy.sock

# 检查代理进程
ps aux | grep serial_proxy_daemon

# 测试Socket连接
echo "test" | socat - UNIX-CONNECT:/tmp/n100_serial_proxy.sock
```

**解决方法**:
```bash
# 重启代理服务
sudo systemctl restart n100-serial-proxy

# 检查Socket权限
sudo chmod 666 /tmp/n100_serial_proxy.sock

# 清理旧Socket文件
sudo rm -f /tmp/n100_serial_proxy.sock
sudo systemctl restart n100-serial-proxy
```

#### 问题4.2: 代理消息丢失

**症状**:
- 客户端发送的消息未到达串口
- 串口数据未转发给客户端

**诊断**:
```bash
# 检查代理日志
journalctl -u n100-serial-proxy -f

# 监控代理进程
strace -p $(pgrep -f "serial_proxy_daemon")
```

**解决方法**:
```bash
# 检查代理缓冲区
# 重启代理服务
# 检查客户端连接状态
```

### 5. 性能问题

#### 问题5.1: 响应速度慢

**症状**:
- 命令执行时间过长
- 系统响应延迟

**诊断**:
```bash
# 监控系统资源
top -p $(pgrep -f "n100")
iostat -x 1

# 测试命令执行时间
time python3 power_ctrl_cli.py led normal
```

**解决方法**:
```bash
# 优化超时参数
# 减少重试次数
# 使用更快的串口波特率
# 优化代码逻辑
```

#### 问题5.2: 内存泄漏

**症状**:
- 进程内存使用持续增长
- 系统内存不足

**诊断**:
```bash
# 监控内存使用
ps -o pid,vsz,rss,comm -p $(pgrep -f "n100")

# 使用内存分析工具
valgrind --tool=memcheck python3 /opt/n100/ctrl/serial_proxy_daemon.py
```

**解决方法**:
```bash
# 定期重启服务
# 修复代码中的内存泄漏
# 设置内存限制
```

## 调试工具

### 1. 串口调试工具

```bash
# minicom - 串口终端
sudo minicom -D /dev/ttyS4 -b 115200

# socat - 多功能网络工具
socat -d -d pty,raw,echo=0 pty,raw,echo=0

# hexdump - 十六进制查看
cat /dev/ttyS4 | hexdump -C
```

### 2. 系统调试工具

```bash
# strace - 系统调用跟踪
strace -e trace=read,write,open -p PID

# lsof - 文件占用查看
lsof -p PID

# netstat/ss - 网络连接查看
ss -x | grep n100
```

### 3. 日志分析工具

```bash
# journalctl - 系统日志
journalctl -u n100-serial-proxy --since "1 hour ago" -o json-pretty

# grep - 日志过滤
journalctl -u n100-serial-proxy | grep ERROR

# tail - 实时日志
tail -f /var/log/n100_*.log
```

## 预防措施

### 1. 监控设置

```bash
# 创建监控脚本
cat > /usr/local/bin/n100-monitor.sh << 'EOF'
#!/bin/bash
# N100系统监控脚本

# 检查服务状态
if ! systemctl is-active --quiet n100-serial-proxy; then
    echo "$(date): 串口代理服务异常" >> /var/log/n100-monitor.log
    systemctl restart n100-serial-proxy
fi

# 检查串口设备
if [ ! -e /dev/ttyS4 ]; then
    echo "$(date): 串口设备不存在" >> /var/log/n100-monitor.log
fi

# 检查进程资源
memory_usage=$(ps -o %mem -p $(pgrep -f "n100") | tail -n +2 | awk '{sum+=$1} END {print sum}')
if (( $(echo "$memory_usage > 10" | bc -l) )); then
    echo "$(date): 内存使用过高: ${memory_usage}%" >> /var/log/n100-monitor.log
fi
EOF

chmod +x /usr/local/bin/n100-monitor.sh

# 添加到crontab
echo "*/5 * * * * /usr/local/bin/n100-monitor.sh" | crontab -
```

### 2. 自动恢复

```bash
# 创建自动恢复脚本
cat > /usr/local/bin/n100-recovery.sh << 'EOF'
#!/bin/bash
# N100系统自动恢复脚本

# 检查并修复串口权限
if [ -e /dev/ttyS4 ]; then
    chmod 666 /dev/ttyS4
fi

# 清理旧的Socket文件
if [ -e /tmp/n100_serial_proxy.sock ]; then
    if ! ss -x | grep -q n100_serial_proxy.sock; then
        rm -f /tmp/n100_serial_proxy.sock
    fi
fi

# 重启异常服务
for service in n100-serial-proxy n100-shutdown-proxy; do
    if systemctl is-failed --quiet $service; then
        systemctl restart $service
        echo "$(date): 重启服务 $service" >> /var/log/n100-recovery.log
    fi
done
EOF

chmod +x /usr/local/bin/n100-recovery.sh
```

### 3. 备份策略

```bash
# 定期备份配置
cat > /usr/local/bin/n100-backup.sh << 'EOF'
#!/bin/bash
# N100系统备份脚本

backup_dir="/backup/n100/$(date +%Y%m%d)"
mkdir -p "$backup_dir"

# 备份服务配置
cp /etc/systemd/system/n100-*.service "$backup_dir/"

# 备份源代码
tar -czf "$backup_dir/n100-ctrl.tar.gz" /opt/n100/ctrl

# 备份日志
journalctl -u n100-serial-proxy --since "1 day ago" > "$backup_dir/proxy.log"
journalctl -u n100-shutdown-proxy --since "1 day ago" > "$backup_dir/shutdown.log"

# 清理旧备份（保留7天）
find /backup/n100 -type d -mtime +7 -exec rm -rf {} \;
EOF

chmod +x /usr/local/bin/n100-backup.sh

# 添加到crontab（每天备份）
echo "0 2 * * * /usr/local/bin/n100-backup.sh" | crontab -
```

## 联系支持

如果以上方法无法解决问题，请联系技术支持：

- **邮箱**: <EMAIL>
- **文档**: https://github.com/n100team/ctrl/wiki
- **问题报告**: https://github.com/n100team/ctrl/issues

提供以下信息以便快速诊断：

1. 系统信息: `uname -a`
2. 服务状态: `systemctl status n100-*`
3. 错误日志: `journalctl -u n100-* --since "1 hour ago"`
4. 硬件信息: `lsusb`, `dmesg | grep ttyS`

---

**版本**: 1.0.0  
**最后更新**: 2025-07-16  
**维护者**: N100 Team
