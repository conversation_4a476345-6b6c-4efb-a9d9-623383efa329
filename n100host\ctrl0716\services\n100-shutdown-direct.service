[Unit]
Description=N100 Shutdown Daemon (Direct Serial)
Documentation=man:n100-shutdown-direct(8)
After=multi-user.target
Wants=multi-user.target

[Service]
Type=simple
ExecStart=/usr/bin/python3 /opt/n100/ctrl/n100_shutdown_daemon_direct.py --port /dev/ttyS4 --log /var/log/n100_shutdown_direct.log
Restart=always
RestartSec=5
User=root
Group=root

# 串口访问权限
SupplementaryGroups=dialout

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=n100-shutdown-direct

# 环境变量
Environment=PYTHONPATH=/opt/n100/ctrl
Environment=PYTHONUNBUFFERED=1

# 安全配置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log /dev/ttyS4

# 资源限制
MemoryMax=30M
CPUQuota=5%

[Install]
WantedBy=multi-user.target
