#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
内存串口模拟器
使用内存队列模拟串口通信，不依赖外部工具
"""

import os
import sys
import time
import threading
import queue
from typing import Optional, Dict, Any

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from protocol import (
        ProtocolFrame, CommandType, FrameConstants,
        validate_frame, create_ack_frame, create_shutdown_request_frame,
        ProtocolParser
    )
except ImportError as e:
    print(f"错误: 无法导入协议模块: {e}")
    sys.exit(1)


class MemorySerial:
    """内存串口模拟器"""
    
    def __init__(self, name: str):
        self.name = name
        self.is_open = True
        self.baudrate = 115200
        self.timeout = 1.0
        self.in_waiting = 0
        
        # 使用队列模拟串口缓冲区
        self._read_queue = queue.Queue()
        self._write_queue = queue.Queue()
        
        # 连接到对端
        self.peer = None
        
        # 统计信息
        self.bytes_sent = 0
        self.bytes_received = 0
    
    def connect_peer(self, peer: 'MemorySerial'):
        """连接到对端串口"""
        self.peer = peer
        peer.peer = self
    
    def write(self, data: bytes):
        """写入数据"""
        if not self.is_open:
            raise Exception("Serial port is closed")
        
        self.bytes_sent += len(data)
        
        # 将数据发送到对端的读取队列
        if self.peer:
            self.peer._read_queue.put(data)
            self.peer.in_waiting += len(data)
    
    def read(self, size: int = 1) -> bytes:
        """读取数据"""
        if not self.is_open:
            raise Exception("Serial port is closed")
        
        result = b''
        end_time = time.time() + self.timeout
        
        while len(result) < size and time.time() < end_time:
            try:
                data = self._read_queue.get(timeout=0.1)
                result += data
                self.in_waiting = max(0, self.in_waiting - len(data))
                self.bytes_received += len(data)
                
                if len(result) >= size:
                    break
                    
            except queue.Empty:
                continue
        
        return result[:size] if result else b''
    
    def flush(self):
        """刷新缓冲区"""
        pass
    
    def reset_input_buffer(self):
        """清空输入缓冲区"""
        while not self._read_queue.empty():
            try:
                self._read_queue.get_nowait()
            except queue.Empty:
                break
        self.in_waiting = 0
    
    def reset_output_buffer(self):
        """清空输出缓冲区"""
        pass
    
    def close(self):
        """关闭串口"""
        self.is_open = False


class PowerBoardSimulator:
    """电源板模拟器（内存版本）"""
    
    def __init__(self, serial_port: MemorySerial):
        self.serial = serial_port
        self.running = False
        self.parser = ProtocolParser()
        
        # 统计信息
        self.stats = {
            'received_commands': 0,
            'sent_acks': 0,
            'sent_shutdown_requests': 0,
            'errors': 0
        }
    
    def start(self):
        """启动模拟器"""
        self.running = True
        self.listen_thread = threading.Thread(target=self._listen_loop, daemon=True)
        self.listen_thread.start()
        print("✅ 电源板模拟器启动成功")
    
    def stop(self):
        """停止模拟器"""
        self.running = False
        print("电源板模拟器已停止")
    
    def _listen_loop(self):
        """监听循环"""
        while self.running:
            try:
                if self.serial.in_waiting > 0:
                    data = self.serial.read(self.serial.in_waiting)
                    if data:
                        self._process_received_data(data)
                
                time.sleep(0.01)
                
            except Exception as e:
                print(f"监听异常: {e}")
                self.stats['errors'] += 1
                time.sleep(0.1)
    
    def _process_received_data(self, data: bytes):
        """处理接收到的数据"""
        print(f"[电源板接收] {data.hex(' ').upper()}")
        
        # 解析帧
        frames = self.parser.feed_data(data)
        
        for frame in frames:
            self._handle_frame(frame)
    
    def _handle_frame(self, frame: ProtocolFrame):
        """处理接收到的帧"""
        print(f"[电源板解析] 命令=0x{frame.command:02X}, 数据={frame.data.hex(' ').upper()}")
        
        self.stats['received_commands'] += 1
        
        # 根据命令类型处理
        if frame.command == CommandType.LED_MODE:
            mode = frame.data[0] if frame.data else 0
            mode_name = "呼吸模式" if mode == 1 else "正常模式"
            print(f"[电源板] LED模式设置: {mode_name}")
            
        elif frame.command == CommandType.BREATH_PERIOD:
            period = frame.data[0] if frame.data else 0
            print(f"[电源板] 呼吸周期设置: {period}秒")
            
        elif frame.command == CommandType.SHUTDOWN_SUCCESS:
            print(f"[电源板] 收到关机成功通知")
            
        elif frame.command == CommandType.ACK:
            print(f"[电源板] 收到ACK应答")
            return  # ACK不需要回复
            
        else:
            print(f"[电源板] 未知命令: 0x{frame.command:02X}")
        
        # 发送ACK应答
        self._send_ack()
    
    def _send_ack(self):
        """发送ACK应答"""
        try:
            ack_frame = create_ack_frame()
            ack_bytes = ack_frame.to_bytes()
            
            self.serial.write(ack_bytes)
            print(f"[电源板发送] ACK应答: {ack_bytes.hex(' ').upper()}")
            self.stats['sent_acks'] += 1
            
        except Exception as e:
            print(f"发送ACK失败: {e}")
            self.stats['errors'] += 1
    
    def send_shutdown_request(self):
        """发送关机请求"""
        try:
            shutdown_frame = create_shutdown_request_frame()
            shutdown_bytes = shutdown_frame.to_bytes()
            
            self.serial.write(shutdown_bytes)
            print(f"[电源板发送] 关机请求: {shutdown_bytes.hex(' ').upper()}")
            self.stats['sent_shutdown_requests'] += 1
            
        except Exception as e:
            print(f"发送关机请求失败: {e}")
            self.stats['errors'] += 1


class CommunicationTester:
    """通信测试器"""
    
    def __init__(self):
        # 创建内存串口对
        self.n100_serial = MemorySerial("N100")
        self.power_serial = MemorySerial("PowerBoard")
        
        # 连接串口对
        self.n100_serial.connect_peer(self.power_serial)
        
        # 创建电源板模拟器
        self.power_simulator = PowerBoardSimulator(self.power_serial)
    
    def test_basic_communication(self) -> bool:
        """测试基本通信"""
        print("\n=== 测试基本通信 ===")
        
        try:
            # 启动电源板模拟器
            self.power_simulator.start()
            time.sleep(0.5)
            
            # 模拟N100发送LED命令
            led_normal_frame = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
            print(f"[N100发送] LED正常模式: {led_normal_frame.hex(' ').upper()}")
            
            self.n100_serial.write(led_normal_frame)
            
            # 等待处理
            time.sleep(1)
            
            # 检查是否收到ACK
            if self.n100_serial.in_waiting > 0:
                response = self.n100_serial.read(self.n100_serial.in_waiting)
                print(f"[N100接收] ACK应答: {response.hex(' ').upper()}")
                
                # 验证ACK格式
                expected_ack = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
                if response == expected_ack:
                    print("✅ 收到正确的ACK应答")
                    return True
                else:
                    print("❌ ACK应答格式不正确")
                    return False
            else:
                print("❌ 没有收到ACK应答")
                return False
                
        except Exception as e:
            print(f"❌ 基本通信测试失败: {e}")
            return False
    
    def test_power_controller(self) -> bool:
        """测试电源控制器"""
        print("\n=== 测试电源控制器 ===")
        
        try:
            # 导入并修改电源控制器以使用内存串口
            from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod
            
            # 创建控制器
            controller = N100PowerController(use_manager=False, max_retries=3, timeout=2.0)
            
            # 替换串口对象
            controller.serial = self.n100_serial
            controller.is_connected = True
            
            print("✅ 电源控制器已连接（内存串口）")
            
            # 测试LED控制
            print("\n1. 测试LED正常模式...")
            success = controller.set_led_mode(LEDMode.NORMAL)
            print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
            
            if success:
                time.sleep(0.5)
                
                print("\n2. 测试LED呼吸模式...")
                success = controller.set_led_mode(LEDMode.BREATH)
                print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
                
                if success:
                    time.sleep(0.5)
                    
                    print("\n3. 测试呼吸周期设置...")
                    success = controller.set_breath_period(BreathPeriod.PERIOD_3S)
                    print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
                    
                    if success:
                        time.sleep(0.5)
                        
                        print("\n4. 测试关机成功消息...")
                        success = controller.send_shutdown_success()
                        print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
            
            return success
            
        except Exception as e:
            print(f"❌ 电源控制器测试失败: {e}")
            return False
    
    def test_shutdown_flow(self) -> bool:
        """测试关机流程"""
        print("\n=== 测试关机流程 ===")
        
        try:
            # 1. 电源板发送关机请求
            print("1. 电源板发送关机请求...")
            self.power_simulator.send_shutdown_request()
            time.sleep(0.5)
            
            # 2. 检查N100是否收到关机请求
            if self.n100_serial.in_waiting > 0:
                request_data = self.n100_serial.read(self.n100_serial.in_waiting)
                print(f"[N100接收] 关机请求: {request_data.hex(' ').upper()}")
                
                # 3. N100发送ACK应答
                ack_frame = create_ack_frame()
                ack_bytes = ack_frame.to_bytes()
                self.n100_serial.write(ack_bytes)
                print(f"[N100发送] ACK应答: {ack_bytes.hex(' ').upper()}")
                
                time.sleep(0.5)
                
                # 4. N100发送关机成功消息
                from protocol import create_shutdown_success_frame
                shutdown_frame = create_shutdown_success_frame()
                shutdown_bytes = shutdown_frame.to_bytes()
                self.n100_serial.write(shutdown_bytes)
                print(f"[N100发送] 关机成功: {shutdown_bytes.hex(' ').upper()}")
                
                time.sleep(0.5)
                
                print("✅ 关机流程测试完成")
                return True
            else:
                print("❌ N100未收到关机请求")
                return False
                
        except Exception as e:
            print(f"❌ 关机流程测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("N100通信系统测试")
        print("=" * 40)
        
        results = {}
        
        try:
            # 1. 基本通信测试
            results['basic_comm'] = self.test_basic_communication()
            
            # 2. 电源控制器测试
            results['power_controller'] = self.test_power_controller()
            
            # 3. 关机流程测试
            results['shutdown_flow'] = self.test_shutdown_flow()
            
            # 显示结果
            print(f"\n=== 测试结果总结 ===")
            for test_name, result in results.items():
                status = "✅ 通过" if result else "❌ 失败"
                print(f"{test_name}: {status}")
            
            # 显示统计信息
            print(f"\n=== 通信统计 ===")
            print(f"电源板接收命令: {self.power_simulator.stats['received_commands']}")
            print(f"电源板发送ACK: {self.power_simulator.stats['sent_acks']}")
            print(f"电源板发送关机请求: {self.power_simulator.stats['sent_shutdown_requests']}")
            print(f"N100发送字节: {self.n100_serial.bytes_sent}")
            print(f"N100接收字节: {self.n100_serial.bytes_received}")
            
            all_passed = all(results.values())
            if all_passed:
                print("\n🎉 所有测试通过！通信功能正常")
            else:
                print("\n❌ 部分测试失败，需要检查问题")
            
            return all_passed
            
        except Exception as e:
            print(f"\n❌ 测试异常: {e}")
            return False
        finally:
            self.power_simulator.stop()


def main():
    """主函数"""
    tester = CommunicationTester()
    
    try:
        success = tester.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n测试被中断")
        return 1


if __name__ == "__main__":
    sys.exit(main())
