#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制系统测试运行器
运行所有测试并生成报告
"""

import sys
import os
import unittest
import time
from io import StringIO

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))

# 导入测试模块
from test_protocol import run_tests as run_protocol_tests
from test_power_ctrl import run_tests as run_power_ctrl_tests
from test_integration import run_tests as run_integration_tests


class TestResult:
    """测试结果类"""
    
    def __init__(self, name, success, duration, details=""):
        self.name = name
        self.success = success
        self.duration = duration
        self.details = details


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.results = []
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
    
    def run_test_suite(self, name, test_func):
        """运行测试套件"""
        print(f"\n{'='*60}")
        print(f"运行 {name} 测试...")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        # 捕获输出
        old_stdout = sys.stdout
        old_stderr = sys.stderr
        stdout_capture = StringIO()
        stderr_capture = StringIO()
        
        try:
            sys.stdout = stdout_capture
            sys.stderr = stderr_capture
            
            success = test_func()
            
        except Exception as e:
            success = False
            stderr_capture.write(f"测试异常: {e}")
        
        finally:
            sys.stdout = old_stdout
            sys.stderr = old_stderr
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 获取输出内容
        stdout_content = stdout_capture.getvalue()
        stderr_content = stderr_capture.getvalue()
        details = stdout_content + stderr_content
        
        # 记录结果
        result = TestResult(name, success, duration, details)
        self.results.append(result)
        
        # 更新统计
        self.total_tests += 1
        if success:
            self.passed_tests += 1
            print(f"✅ {name} 测试通过 ({duration:.2f}秒)")
        else:
            self.failed_tests += 1
            print(f"❌ {name} 测试失败 ({duration:.2f}秒)")
            if stderr_content:
                print(f"错误信息: {stderr_content}")
        
        return success
    
    def run_all_tests(self):
        """运行所有测试"""
        print("N100电源控制系统测试套件")
        print("=" * 60)
        print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        start_time = time.time()
        
        # 运行各个测试套件
        test_suites = [
            ("协议模块", run_protocol_tests),
            ("电源控制器", run_power_ctrl_tests),
            ("集成测试", run_integration_tests),
        ]
        
        for name, test_func in test_suites:
            self.run_test_suite(name, test_func)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 生成报告
        self.generate_report(total_duration)
        
        return self.failed_tests == 0
    
    def generate_report(self, total_duration):
        """生成测试报告"""
        print(f"\n{'='*60}")
        print("测试报告")
        print(f"{'='*60}")
        
        print(f"总测试套件: {self.total_tests}")
        print(f"通过: {self.passed_tests}")
        print(f"失败: {self.failed_tests}")
        print(f"成功率: {self.passed_tests/self.total_tests*100:.1f}%")
        print(f"总耗时: {total_duration:.2f}秒")
        
        print(f"\n详细结果:")
        print("-" * 60)
        
        for result in self.results:
            status = "✅ 通过" if result.success else "❌ 失败"
            print(f"{result.name:<15} {status:<8} {result.duration:>6.2f}秒")
        
        # 失败详情
        failed_results = [r for r in self.results if not r.success]
        if failed_results:
            print(f"\n失败详情:")
            print("-" * 60)
            
            for result in failed_results:
                print(f"\n{result.name}:")
                if result.details:
                    # 只显示错误相关的行
                    lines = result.details.split('\n')
                    error_lines = [line for line in lines if 'error' in line.lower() or 'fail' in line.lower() or 'exception' in line.lower()]
                    if error_lines:
                        for line in error_lines[:5]:  # 最多显示5行
                            print(f"  {line}")
                    else:
                        print(f"  {result.details[:200]}...")  # 显示前200个字符
        
        # 保存报告到文件
        self.save_report_to_file(total_duration)
    
    def save_report_to_file(self, total_duration):
        """保存报告到文件"""
        try:
            report_file = os.path.join(os.path.dirname(__file__), 'test_report.txt')
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("N100电源控制系统测试报告\n")
                f.write("=" * 60 + "\n")
                f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总测试套件: {self.total_tests}\n")
                f.write(f"通过: {self.passed_tests}\n")
                f.write(f"失败: {self.failed_tests}\n")
                f.write(f"成功率: {self.passed_tests/self.total_tests*100:.1f}%\n")
                f.write(f"总耗时: {total_duration:.2f}秒\n\n")
                
                f.write("详细结果:\n")
                f.write("-" * 60 + "\n")
                
                for result in self.results:
                    status = "通过" if result.success else "失败"
                    f.write(f"{result.name:<15} {status:<8} {result.duration:>6.2f}秒\n")
                
                # 失败详情
                failed_results = [r for r in self.results if not r.success]
                if failed_results:
                    f.write(f"\n失败详情:\n")
                    f.write("-" * 60 + "\n")
                    
                    for result in failed_results:
                        f.write(f"\n{result.name}:\n")
                        if result.details:
                            f.write(result.details)
                        f.write("\n")
            
            print(f"\n测试报告已保存到: {report_file}")
            
        except Exception as e:
            print(f"保存报告失败: {e}")


def main():
    """主函数"""
    runner = TestRunner()
    
    try:
        success = runner.run_all_tests()
        
        if success:
            print(f"\n🎉 所有测试通过！")
            return 0
        else:
            print(f"\n❌ 部分测试失败！")
            return 1
    
    except KeyboardInterrupt:
        print(f"\n⚠️  测试被用户中断")
        return 1
    
    except Exception as e:
        print(f"\n❌ 测试运行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit(main())
