#!/bin/bash

# N100关机流程调试脚本
# 重启服务并进行详细的调试测试

set -e

echo "=== N100关机流程调试 ==="
echo "调试时间: $(date)"
echo ""

# 函数：等待用户确认
wait_for_user() {
    read -p "按回车键继续..."
}

# 函数：显示实时日志
show_logs() {
    echo "显示实时日志 (按Ctrl+C停止):"
    sudo journalctl -u n100-serial-proxy -u n100-shutdown-proxy -f &
    LOG_PID=$!
    sleep 2
    kill $LOG_PID 2>/dev/null || true
}

echo "1. 停止现有服务..."
sudo systemctl stop n100-shutdown-proxy n100-serial-proxy
echo "✅ 服务已停止"

echo ""
echo "2. 启动串口代理服务..."
sudo systemctl start n100-serial-proxy
sleep 2

if systemctl is-active --quiet n100-serial-proxy; then
    echo "✅ 串口代理服务启动成功"
else
    echo "❌ 串口代理服务启动失败"
    sudo journalctl -u n100-serial-proxy -n 10
    exit 1
fi

echo ""
echo "3. 启动关机守护进程..."
sudo systemctl start n100-shutdown-proxy
sleep 2

if systemctl is-active --quiet n100-shutdown-proxy; then
    echo "✅ 关机守护进程启动成功"
else
    echo "❌ 关机守护进程启动失败"
    sudo journalctl -u n100-shutdown-proxy -n 10
    exit 1
fi

echo ""
echo "4. 检查服务状态..."
systemctl status n100-serial-proxy --no-pager -l
echo ""
systemctl status n100-shutdown-proxy --no-pager -l

echo ""
echo "5. 查看启动日志..."
echo "串口代理启动日志:"
sudo journalctl -u n100-serial-proxy --since "2 minutes ago" --no-pager

echo ""
echo "关机守护进程启动日志:"
sudo journalctl -u n100-shutdown-proxy --since "2 minutes ago" --no-pager

echo ""
echo "6. 准备进行测试..."
echo "现在将发送关机请求进行测试"
echo "注意: 这是调试测试，不会真正关机"

wait_for_user

echo ""
echo "7. 发送关机请求..."

# 方法1: 通过代理发送
echo "方法1: 通过Python脚本发送关机请求"
cd src
python3 -c "
import sys, os
sys.path.insert(0, '.')
from serial_proxy_client import SerialProxyClient
client = SerialProxyClient('debug_test')
if client.connect():
    shutdown_request = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
    print(f'发送关机请求: {shutdown_request.hex(\" \").upper()}')
    result = client.send_data(shutdown_request)
    print(f'发送结果: {\"成功\" if result else \"失败\"}')
    client.disconnect()
else:
    print('无法连接到代理服务')
"
cd ..

echo ""
echo "等待处理..."
sleep 3

echo ""
echo "方法2: 直接向串口发送"
if [ -w /dev/ttyS4 ]; then
    echo "直接向串口发送关机请求..."
    echo -ne '\xAA\x01\x13\xED\x55' | sudo tee /dev/ttyS4 > /dev/null
    echo "✅ 关机请求已发送到串口"
else
    echo "⚠️  无法写入串口设备"
fi

echo ""
echo "等待处理..."
sleep 5

echo ""
echo "8. 检查处理结果..."

echo "最近的串口代理日志:"
sudo journalctl -u n100-serial-proxy --since "1 minute ago" --no-pager

echo ""
echo "最近的关机守护进程日志:"
sudo journalctl -u n100-shutdown-proxy --since "1 minute ago" --no-pager

echo ""
echo "9. 分析结果..."
echo "检查关键词..."

# 检查是否有关机请求处理记录
if sudo journalctl -u n100-shutdown-proxy --since "2 minutes ago" | grep -q "关机请求"; then
    echo "✅ 发现关机请求处理记录"
else
    echo "❌ 未发现关机请求处理记录"
fi

# 检查是否有ACK发送记录
if sudo journalctl -u n100-serial-proxy --since "2 minutes ago" | grep -q "ACK"; then
    echo "✅ 发现ACK发送记录"
else
    echo "❌ 未发现ACK发送记录"
fi

# 检查是否有关机成功记录
if sudo journalctl -u n100-shutdown-proxy --since "2 minutes ago" | grep -q "关机成功"; then
    echo "✅ 发现关机成功记录"
else
    echo "❌ 未发现关机成功记录"
fi

echo ""
echo "10. 调试总结..."
echo "如果看到:"
echo "  ✅ 关机请求处理记录 - 说明关机守护进程接收到了消息"
echo "  ✅ ACK发送记录 - 说明串口代理发送了ACK应答"
echo "  ✅ 关机成功记录 - 说明关机流程正常执行"
echo ""
echo "如果没有看到关机请求处理记录，可能的原因:"
echo "  1. 消息过滤器设置问题"
echo "  2. 消息转发机制问题"
echo "  3. 关机守护进程接收逻辑问题"

echo ""
echo "🎉 调试测试完成！"
echo ""
echo "提示: 可以运行以下命令查看详细日志分析:"
echo "  python3 check_shutdown_logs.py"
