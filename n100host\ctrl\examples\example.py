#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制器使用示例
演示如何使用N100PowerController类
"""

import time
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))

from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod


def ack_callback(command):
    """ACK接收回调函数"""
    print(f"[回调] 收到ACK应答，命令: 0x{command:02X}")


def error_callback(error_msg):
    """错误回调函数"""
    print(f"[回调] 发生错误: {error_msg}")


def basic_example():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建控制器实例
    controller = N100PowerController(
        port='/dev/ttyS4',      # 串口设备
        baudrate=115200,        # 波特率
        timeout=1.0,           # 超时时间
        max_retries=10         # 最大重试次数
    )
    
    # 设置回调函数
    controller.set_ack_callback(ack_callback)
    controller.set_error_callback(error_callback)
    
    # 连接串口
    if not controller.connect():
        print("错误: 无法连接到串口")
        return False
    
    try:
        # 设置LED为正常模式
        print("\n1. 设置LED为正常模式")
        if controller.set_led_normal():
            print("✓ LED正常模式设置成功")
        else:
            print("✗ LED正常模式设置失败")
        
        time.sleep(1)
        
        # 设置LED为呼吸模式
        print("\n2. 设置LED为呼吸模式")
        if controller.set_led_breath():
            print("✓ LED呼吸模式设置成功")
        else:
            print("✗ LED呼吸模式设置失败")
        
        time.sleep(1)
        
        # 设置呼吸周期为3秒
        print("\n3. 设置呼吸周期为3秒")
        if controller.set_breath_3s():
            print("✓ 呼吸周期设置成功")
        else:
            print("✗ 呼吸周期设置失败")
        
        time.sleep(1)
        
        # 发送关机成功消息
        print("\n4. 发送关机成功消息")
        if controller.send_shutdown_success():
            print("✓ 关机成功消息发送成功")
        else:
            print("✗ 关机成功消息发送失败")
        
        return True
        
    finally:
        controller.disconnect()


def advanced_example():
    """高级使用示例"""
    print("\n=== 高级使用示例 ===")
    
    controller = N100PowerController()
    
    # 设置回调函数
    controller.set_ack_callback(ack_callback)
    controller.set_error_callback(error_callback)
    
    if not controller.connect():
        print("错误: 无法连接到串口")
        return False
    
    try:
        # 使用枚举值设置LED模式
        print("\n1. 使用枚举值设置LED模式")
        for mode in [LEDMode.NORMAL, LEDMode.BREATH]:
            mode_name = "正常" if mode == LEDMode.NORMAL else "呼吸"
            print(f"设置LED为{mode_name}模式...")
            if controller.set_led_mode(mode):
                print(f"✓ LED{mode_name}模式设置成功")
            else:
                print(f"✗ LED{mode_name}模式设置失败")
            time.sleep(1)
        
        # 测试所有呼吸周期
        print("\n2. 测试所有呼吸周期")
        for period in [BreathPeriod.PERIOD_1S, BreathPeriod.PERIOD_3S, BreathPeriod.PERIOD_5S]:
            print(f"设置呼吸周期为{period}秒...")
            if controller.set_breath_period(period):
                print(f"✓ {period}秒呼吸周期设置成功")
            else:
                print(f"✗ {period}秒呼吸周期设置失败")
            time.sleep(1)
        
        # 发送自定义命令
        print("\n3. 发送自定义命令")
        if controller.send_custom_command(0x01, b'\x00'):  # 等同于设置LED正常模式
            print("✓ 自定义命令发送成功")
        else:
            print("✗ 自定义命令发送失败")
        
        # 查询状态
        print("\n4. 查询串口状态")
        info = controller.get_port_info()
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        return True
        
    finally:
        controller.disconnect()


def test_all_commands():
    """测试所有预定义的消息帧"""
    print("\n=== 测试所有预定义消息帧 ===")
    
    controller = N100PowerController()
    controller.set_ack_callback(ack_callback)
    controller.set_error_callback(error_callback)
    
    if not controller.connect():
        print("错误: 无法连接到串口")
        return False
    
    try:
        test_cases = [
            ("AA 02 01 00 FF 55", "设置正常模式", lambda: controller.set_led_normal()),
            ("AA 02 01 01 FE 55", "设置呼吸模式", lambda: controller.set_led_breath()),
            ("AA 02 02 01 FD 55", "1秒周期", lambda: controller.set_breath_1s()),
            ("AA 02 02 03 FB 55", "3秒周期", lambda: controller.set_breath_3s()),
            ("AA 02 02 05 F9 55", "5秒周期", lambda: controller.set_breath_5s()),
            ("AA 01 03 FD 55", "关机成功", lambda: controller.send_shutdown_success()),
        ]
        
        success_count = 0
        for expected_frame, description, command_func in test_cases:
            print(f"\n测试: {description}")
            print(f"期望帧: {expected_frame}")
            
            if command_func():
                success_count += 1
                print(f"✓ {description} - 成功")
            else:
                print(f"✗ {description} - 失败")
            
            time.sleep(1)
        
        print(f"\n测试结果: {success_count}/{len(test_cases)} 个命令成功")
        return success_count == len(test_cases)
        
    finally:
        controller.disconnect()


def main():
    """主函数"""
    print("N100电源控制器使用示例")
    print("=" * 50)
    
    try:
        # 运行基本示例
        if not basic_example():
            print("基本示例执行失败")
            return 1
        
        time.sleep(2)
        
        # 运行高级示例
        if not advanced_example():
            print("高级示例执行失败")
            return 1
        
        time.sleep(2)
        
        # 测试所有命令
        if not test_all_commands():
            print("命令测试执行失败")
            return 1
        
        print("\n所有示例执行完成!")
        return 0
        
    except KeyboardInterrupt:
        print("\n用户中断")
        return 1
    except Exception as e:
        print(f"\n执行过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
