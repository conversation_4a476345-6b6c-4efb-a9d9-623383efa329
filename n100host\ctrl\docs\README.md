# N100电源控制器

这是一个用于N100向电源板发送控制命令的Python程序包。N100和电源板通过串口(ttyS4)进行通信，使用自定义的消息帧协议。

## 功能特性

- 支持设置LED模式（正常/呼吸）
- 支持设置呼吸灯周期（1秒/3秒/5秒）
- 支持发送关机成功消息
- 支持自定义命令发送
- 自动重试机制
- ACK应答验证
- 回调函数支持
- 命令行工具

## 消息帧格式

### 帧结构
```
| 帧头 | 长度 | 命令 | 数据 | 校验和 | 帧尾 |
| 0xAA | 1字节 | 1字节 | N字节 | 1字节 | 0x55 |
```

### 支持的命令帧

| 命令 | 帧数据 | 说明 |
|------|--------|------|
| 设置正常模式 | `AA 02 01 00 FF 55` | LED正常模式 |
| 设置呼吸模式 | `AA 02 01 01 FE 55` | LED呼吸模式 |
| 1秒周期 | `AA 02 02 01 FD 55` | 呼吸灯1秒周期 |
| 3秒周期 | `AA 02 02 03 FB 55` | 呼吸灯3秒周期 |
| 5秒周期 | `AA 02 02 05 F9 55` | 呼吸灯5秒周期 |
| 关机成功 | `AA 01 03 FD 55` | 关机成功消息 |

### 应答帧
- 通用应答: `AA 01 80 80 55`

## 安装依赖

```bash
pip install pyserial
```

## 使用方法

### 1. 作为Python模块使用

```python
from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod

# 创建控制器实例
controller = N100PowerController(port='/dev/ttyS4')

# 连接串口
if controller.connect():
    # 设置LED模式
    controller.set_led_normal()      # 正常模式
    controller.set_led_breath()      # 呼吸模式
    
    # 设置呼吸周期
    controller.set_breath_1s()       # 1秒周期
    controller.set_breath_3s()       # 3秒周期
    controller.set_breath_5s()       # 5秒周期
    
    # 发送关机成功
    controller.send_shutdown_success()
    
    # 断开连接
    controller.disconnect()
```

### 2. 使用命令行工具

```bash
# 设置LED模式
python power_ctrl_cli.py led normal    # 正常模式
python power_ctrl_cli.py led breath    # 呼吸模式

# 设置呼吸周期
python power_ctrl_cli.py breath 1      # 1秒周期
python power_ctrl_cli.py breath 3      # 3秒周期
python power_ctrl_cli.py breath 5      # 5秒周期

# 发送关机成功
python power_ctrl_cli.py shutdown

# 发送自定义命令
python power_ctrl_cli.py custom 01 00  # 命令0x01，数据0x00

# 运行测试序列
python power_ctrl_cli.py test

# 查询串口状态
python power_ctrl_cli.py status

# 指定串口参数
python power_ctrl_cli.py --port /dev/ttyS4 --baudrate 115200 led normal
```

### 3. 运行示例程序

```bash
python example.py
```

## API参考

### N100PowerController类

#### 构造函数
```python
N100PowerController(port='/dev/ttyS4', baudrate=115200, timeout=1.0, max_retries=10)
```

#### 主要方法

- `connect()` - 连接串口
- `disconnect()` - 断开串口
- `set_led_mode(mode)` - 设置LED模式
- `set_breath_period(period)` - 设置呼吸周期
- `send_shutdown_success()` - 发送关机成功
- `send_custom_command(command, data)` - 发送自定义命令

#### 便捷方法

- `set_led_normal()` - 设置LED正常模式
- `set_led_breath()` - 设置LED呼吸模式
- `set_breath_1s()` - 设置1秒呼吸周期
- `set_breath_3s()` - 设置3秒呼吸周期
- `set_breath_5s()` - 设置5秒呼吸周期

#### 回调设置

- `set_ack_callback(callback)` - 设置ACK接收回调
- `set_error_callback(callback)` - 设置错误回调

#### 状态查询

- `is_port_connected()` - 检查串口连接状态
- `get_port_info()` - 获取串口信息

## 错误处理

程序具有完善的错误处理机制：

1. **连接错误**: 串口无法打开时会返回错误
2. **超时处理**: 未收到ACK应答时会自动重试
3. **校验错误**: 接收到无效帧时会忽略并继续等待
4. **异常捕获**: 所有异常都会被捕获并记录

## 调试信息

程序会输出详细的调试信息：

- 发送的帧数据（16进制格式）
- 接收的原始数据
- ACK应答确认
- 重试信息
- 错误信息

## 注意事项

1. 确保串口设备 `/dev/ttyS4` 存在且有访问权限
2. 确保电源板已正确连接并处于工作状态
3. 每个命令发送后会等待ACK应答，超时会自动重试
4. 建议在命令间添加适当的延迟以避免冲突

## 文件说明

- `n100_power_ctrl.py` - 核心控制器类
- `power_ctrl_cli.py` - 命令行工具
- `example.py` - 使用示例
- `README.md` - 说明文档
