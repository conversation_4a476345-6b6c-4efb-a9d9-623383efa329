#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的关机流程测试
直接测试关机守护进程是否能接收消息
"""

import os
import sys
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

try:
    from serial_proxy_client import SerialProxyClient
    from serial_proxy_daemon import ProxyMessage, MessageType
except ImportError as e:
    print(f"错误: 无法导入模块: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


def test_connection():
    """测试连接"""
    print("=== 测试串口代理连接 ===")
    
    client = SerialProxyClient("simple_test")
    
    if client.connect():
        print("✅ 成功连接到串口代理")
        client.disconnect()
        return True
    else:
        print("❌ 无法连接到串口代理")
        return False


def test_shutdown_request():
    """测试关机请求"""
    print("\n=== 测试关机请求发送 ===")
    
    client = SerialProxyClient("shutdown_test")
    received_messages = []
    
    def on_message(message):
        received_messages.append(message)
        print(f"收到消息: 命令=0x{message.command:02X}, 数据={message.data.hex(' ').upper()}")
    
    def on_error(error_msg):
        print(f"错误: {error_msg}")
    
    client.set_message_callback(on_message)
    client.set_error_callback(on_error)
    
    if not client.connect():
        print("❌ 无法连接到串口代理")
        return False
    
    try:
        # 发送关机请求
        shutdown_request = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
        print(f"发送关机请求: {shutdown_request.hex(' ').upper()}")
        
        if client.send_data(shutdown_request):
            print("✅ 关机请求发送成功")
        else:
            print("❌ 关机请求发送失败")
            return False
        
        # 等待响应
        print("等待响应...")
        time.sleep(5)
        
        print(f"收到 {len(received_messages)} 条消息")
        
        # 检查是否收到关机成功消息
        shutdown_success = False
        for msg in received_messages:
            if msg.command == 0x03:  # SHUTDOWN_SUCCESS
                shutdown_success = True
                print("✅ 收到关机成功消息")
                break
        
        if not shutdown_success:
            print("⚠️  未收到关机成功消息")
        
        return True
    
    finally:
        client.disconnect()


def main():
    """主函数"""
    print("N100关机流程简单测试")
    print("=" * 40)
    
    # 测试连接
    if not test_connection():
        print("\n❌ 连接测试失败，请检查串口代理服务")
        return 1
    
    # 测试关机请求
    if not test_shutdown_request():
        print("\n❌ 关机请求测试失败")
        return 1
    
    print("\n🎉 测试完成")
    return 0


if __name__ == "__main__":
    exit(main())
