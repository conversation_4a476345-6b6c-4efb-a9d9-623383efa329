# N100电源控制系统 .gitignore

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# 翻译
*.mo
*.pot

# Django相关
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask相关
instance/
.webassets-cache

# Scrapy相关
.scrapy

# Sphinx文档
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery相关
celerybeat-schedule
celerybeat.pid

# SageMath解析文件
*.sage.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre类型检查器
.pyre/

# pytype静态类型分析器
.pytype/

# Cython调试符号
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Linux
*~

# Windows
*.tmp
*.temp

# 项目特定文件
# 配置文件
config.yml
config.yaml
*.conf

# 日志文件
*.log
*.log.*
logs/
log/

# 备份文件
backup/
*.backup
*.bak
*~

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 测试报告
test_report.txt
test_results/
coverage_html/

# 运行时文件
*.pid
*.sock
*.socket

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 证书和密钥
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx

# 压缩文件
*.zip
*.tar.gz
*.tar.bz2
*.tar.xz
*.rar
*.7z

# 编译文件
*.o
*.a
*.so
*.dll
*.exe

# 文档生成
docs/_build/
docs/build/
site/

# 包管理器
node_modules/
bower_components/

# 本地开发
.local/
local/

# 缓存
.cache/
cache/

# 锁文件
*.lock

# 编辑器临时文件
*.swp
*.swo
*~
.#*
#*#

# 项目特定忽略
# 安装目录（如果在项目中）
install/
deployed/

# 服务文件（如果在项目中）
*.service

# 脚本执行日志
script_*.log

# 测试数据
test_data/
mock_data/

# 性能分析
*.prof
*.profile

# 内存转储
*.dump
core.*

# 调试文件
debug/
*.debug

# 本地配置覆盖
local_config.*
dev_config.*

# 用户特定文件
user_*
personal_*

# 实验性代码
experimental/
sandbox/
playground/

# 第三方工具
.terraform/
.vagrant/

# 容器相关
.dockerignore
docker-compose.override.yml

# 监控和分析
.sonarqube/
.scannerwork/

# 安全扫描
security_scan_*
vulnerability_*

# 性能测试
performance_*
benchmark_*

# 部署相关
deploy_*
release_*

# 文档草稿
draft_*
wip_*

# 个人笔记
notes.txt
notes.md
TODO.txt
FIXME.txt

# 临时补丁
*.patch
*.diff

# 自动生成的文件
auto_*
generated_*

# 特定环境文件
.env.local
.env.development
.env.test
.env.production
