# N100主机控制系统

N100主机控制系统是一个用于N100主机与电源板通信的完整解决方案，支持LED控制、呼吸灯设置和关机管理等功能。

## 🏗️ 工程架构

本系统提供两种架构模式，可根据实际需求选择：

### 1. 代理串口服务架构（推荐）
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   电源控制CLI   │    │   关机守护进程   │    │   其他客户端    │
│power_ctrl_cli.py│    │n100_shutdown_    │    │                 │
│                 │    │daemon_proxy.py   │    │                 │
└─────────┬───────┘    └─────────┬────────┘    └─────────┬───────┘
          │                      │                       │
          │              Unix Socket 通信                │
          │                      │                       │
          └──────────────────────┼───────────────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │     串口代理守护进程     │
                    │  serial_proxy_daemon.py │
                    │                         │
                    │  - 独占 /dev/ttyS4      │
                    │  - 消息路由和转发       │
                    │  - 自动ACK应答          │
                    └────────────┬────────────┘
                                 │
                            ┌────▼────┐
                            │ ttyS4   │
                            │ 串口    │
                            └────┬────┘
                                 │
                            ┌────▼────┐
                            │ 电源板  │
                            └─────────┘
```

**优势：**
- 避免串口冲突，多个进程可同时访问
- 统一的消息路由和管理
- 自动ACK应答处理
- 更好的错误处理和恢复

### 2. 直接关机服务架构
```
┌─────────────────┐                    ┌─────────────────┐
│   电源控制CLI   │                    │  直接关机守护   │
│power_ctrl_cli.py│                    │n100_shutdown_   │
│                 │                    │daemon_direct.py │
└─────────┬───────┘                    └─────────┬───────┘
          │                                      │
          │              直接串口访问             │
          │                                      │
          └──────────────────┬───────────────────┘
                             │
                        ┌────▼────┐
                        │ ttyS4   │
                        │ 串口    │
                        └────┬────┘
                             │
                        ┌────▼────┐
                        │ 电源板  │
                        └─────────┘
```

**优势：**
- 架构简单，部署容易
- 直接串口访问，延迟更低
- 适合单一进程访问场景

## 📁 文件结构

```
n100host/ctrl0716/
├── README.md                           # 主要说明文档
├── requirements.txt                    # Python依赖
├── setup.py                           # 安装配置
├── src/                               # 源代码目录
│   ├── __init__.py                    # 模块初始化
│   ├── protocol.py                    # 通信协议定义
│   ├── n100_power_ctrl.py            # 核心电源控制器
│   ├── power_ctrl_cli.py              # 命令行工具
│   ├── serial_proxy_daemon.py         # 串口代理守护进程
│   ├── serial_proxy_client.py         # 串口代理客户端
│   ├── n100_shutdown_daemon_proxy.py  # 关机守护进程(代理版)
│   └── n100_shutdown_daemon_direct.py # 关机守护进程(直接版)
├── services/                          # 系统服务配置
│   ├── n100-serial-proxy.service      # 串口代理服务
│   ├── n100-shutdown-proxy.service    # 关机服务(代理版)
│   └── n100-shutdown-direct.service   # 关机服务(直接版)
├── scripts/                           # 部署和管理脚本
│   ├── install_proxy_system.sh        # 安装代理架构
│   ├── install_direct_system.sh       # 安装直接架构
│   ├── uninstall.sh                   # 卸载脚本
│   └── test_system.sh                 # 系统测试脚本
├── examples/                          # 使用示例
│   ├── basic_example.py               # 基础使用示例
│   └── advanced_example.py            # 高级使用示例
├── tests/                             # 测试文件
│   ├── test_protocol.py               # 协议测试
│   ├── test_power_ctrl.py             # 电源控制测试
│   └── test_integration.py            # 集成测试
└── docs/                              # 详细文档
    ├── PROTOCOL.md                    # 通信协议文档
    ├── API.md                         # API参考文档
    ├── DEPLOYMENT.md                  # 部署指南
    └── TROUBLESHOOTING.md             # 故障排除
```

## 🚀 快速开始

### 1. 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt

# 或使用包管理器安装
pip install pyserial
```

### 2. 选择架构并部署

#### 代理架构（推荐）
```bash
# 安装代理架构
sudo ./scripts/install_proxy_system.sh

# 检查服务状态
systemctl status n100-serial-proxy
systemctl status n100-shutdown-proxy
```

#### 直接架构
```bash
# 安装直接架构
sudo ./scripts/install_direct_system.sh

# 检查服务状态
systemctl status n100-shutdown-direct
```

### 3. 基本使用

#### 命令行工具
```bash
# 设置LED模式
python src/power_ctrl_cli.py led normal    # 正常模式
python src/power_ctrl_cli.py led breath    # 呼吸模式

# 设置呼吸周期
python src/power_ctrl_cli.py breath 1      # 1秒周期
python src/power_ctrl_cli.py breath 3      # 3秒周期
python src/power_ctrl_cli.py breath 5      # 5秒周期

# 发送关机成功
python src/power_ctrl_cli.py shutdown

# 查看帮助
python src/power_ctrl_cli.py --help
```

#### Python API
```python
from src.n100_power_ctrl import N100PowerController

# 创建控制器
controller = N100PowerController(port='/dev/ttyS4')

# 连接并控制
if controller.connect():
    controller.set_led_breath()      # 设置呼吸模式
    controller.set_breath_3s()       # 设置3秒周期
    controller.send_shutdown_success()  # 发送关机成功
    controller.disconnect()
```

## 📋 通信协议

### 消息帧格式
```
| 帧头 | 长度 | 命令 | 数据 | 校验和 | 帧尾 |
| 0xAA | 1字节 | 1字节 | N字节 | 1字节 | 0x55 |
```

### 支持的命令
| 命令 | 帧数据 | 说明 |
|------|--------|------|
| 设置正常模式 | `AA 02 01 00 FF 55` | LED正常模式 |
| 设置呼吸模式 | `AA 02 01 01 FE 55` | LED呼吸模式 |
| 1秒周期 | `AA 02 02 01 FD 55` | 呼吸灯1秒周期 |
| 3秒周期 | `AA 02 02 03 FB 55` | 呼吸灯3秒周期 |
| 5秒周期 | `AA 02 02 05 F9 55` | 呼吸灯5秒周期 |
| 关机成功 | `AA 01 03 FD 55` | 关机成功消息 |
| 关机请求 | `AA 01 13 ED 55` | 关机请求(电源板→N100) |
| 通用应答 | `AA 01 80 80 55` | ACK应答 |

## 🔧 部署步骤

详细的部署步骤请参考 [docs/DEPLOYMENT.md](docs/DEPLOYMENT.md)

### 代理架构部署
1. 运行安装脚本：`sudo ./scripts/install_proxy_system.sh`
2. 验证服务状态：`systemctl status n100-serial-proxy n100-shutdown-proxy`
3. 测试功能：`./scripts/test_system.sh`

### 直接架构部署
1. 运行安装脚本：`sudo ./scripts/install_direct_system.sh`
2. 验证服务状态：`systemctl status n100-shutdown-direct`
3. 测试功能：`./scripts/test_system.sh`

## 🧪 测试

### 单元测试

```bash
# 运行所有测试
python tests/run_all_tests.py

# 运行特定测试
python tests/test_protocol.py      # 协议测试
python tests/test_power_ctrl.py    # 电源控制器测试
python tests/test_integration.py   # 集成测试
```

### 系统测试

```bash
# 自动检测架构并测试
./scripts/test_system.sh

# 代理架构测试
n100-test-proxy

# 直接架构测试
n100-test-direct
```

### 功能测试

```bash
# 基础功能测试
python examples/basic_example.py

# 高级功能测试
python examples/advanced_example.py
```

## 📚 文档

- [通信协议文档](docs/PROTOCOL.md) - 详细的协议规范
- [API参考文档](docs/API.md) - 完整的API文档
- [部署指南](docs/DEPLOYMENT.md) - 详细的部署说明
- [故障排除](docs/TROUBLESHOOTING.md) - 常见问题解决

## 🔍 故障排除

### 常见问题

1. **串口权限问题**
   ```bash
   sudo chmod 666 /dev/ttyS4
   sudo usermod -a -G dialout $USER
   ```

2. **服务启动失败**
   ```bash
   journalctl -u n100-serial-proxy -f
   journalctl -u n100-shutdown-proxy -f
   ```

3. **通信超时**
   - 检查串口连接
   - 检查电源板状态
   - 增加重试次数

更多故障排除信息请参考 [docs/TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md)

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**版本**: 1.0.0  
**更新日期**: 2025-07-16  
**维护者**: N100 Team
