#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制系统优化版本测试脚本

测试功能：
1. 串口管理器功能测试
2. 双向通信测试
3. 多客户端并发测试
4. 关机流程测试
5. 协议解析测试
"""

import os
import sys
import time
import threading
import unittest
from unittest.mock import Mock, patch

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))

try:
    from serial_manager import SerialManager, MessageFrame, MessageType, get_serial_manager
    from protocol import (
        ProtocolFrame, CommandType, LEDMode, BreathPeriod,
        create_led_mode_frame, create_breath_period_frame,
        create_shutdown_success_frame, create_ack_frame,
        validate_frame, calculate_checksum
    )
    from n100_power_ctrl import N100PowerController
    from n100_shutdown_daemon import N100ShutdownDaemon
except ImportError as e:
    print(f"错误: 无法导入测试模块: {e}")
    sys.exit(1)


class TestProtocol(unittest.TestCase):
    """协议测试类"""
    
    def test_frame_creation(self):
        """测试帧创建"""
        # 测试LED模式帧
        frame = create_led_mode_frame(LEDMode.BREATH)
        self.assertEqual(frame.command, CommandType.LED_MODE)
        self.assertEqual(frame.data, bytes([LEDMode.BREATH]))
        
        # 测试呼吸周期帧
        frame = create_breath_period_frame(BreathPeriod.PERIOD_3S)
        self.assertEqual(frame.command, CommandType.BREATH_PERIOD)
        self.assertEqual(frame.data, bytes([BreathPeriod.PERIOD_3S]))
        
        # 测试关机成功帧
        frame = create_shutdown_success_frame()
        self.assertEqual(frame.command, CommandType.SHUTDOWN_SUCCESS)
        self.assertEqual(frame.data, b'')
    
    def test_frame_serialization(self):
        """测试帧序列化"""
        frame = create_led_mode_frame(LEDMode.NORMAL)
        frame_bytes = frame.to_bytes()
        
        # 验证帧格式
        self.assertEqual(frame_bytes[0], 0xAA)  # 帧头
        self.assertEqual(frame_bytes[1], 0x02)  # 长度
        self.assertEqual(frame_bytes[2], CommandType.LED_MODE)  # 命令
        self.assertEqual(frame_bytes[3], LEDMode.NORMAL)  # 数据
        self.assertEqual(frame_bytes[-1], 0x55)  # 帧尾
    
    def test_frame_validation(self):
        """测试帧验证"""
        # 有效帧
        valid_frame = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
        self.assertTrue(validate_frame(valid_frame))
        
        # 无效帧头
        invalid_frame = bytes([0xBB, 0x02, 0x01, 0x00, 0xFF, 0x55])
        self.assertFalse(validate_frame(invalid_frame))
        
        # 无效校验和
        invalid_checksum = bytes([0xAA, 0x02, 0x01, 0x00, 0xFE, 0x55])
        self.assertFalse(validate_frame(invalid_checksum))
    
    def test_checksum_calculation(self):
        """测试校验和计算"""
        data = bytes([0x01, 0x00])  # 命令+数据
        checksum = calculate_checksum(data)
        
        # 验证校验和计算
        expected = ((~(0x01 + 0x00)) + 1) & 0xFF
        self.assertEqual(checksum, expected)


class TestSerialManager(unittest.TestCase):
    """串口管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.manager = SerialManager(port='/dev/null')  # 使用虚拟端口
    
    def test_client_registration(self):
        """测试客户端注册"""
        # 注册客户端
        success = self.manager.register_client('test_client')
        self.assertTrue(success)
        
        # 重复注册应该失败
        success = self.manager.register_client('test_client')
        self.assertFalse(success)
        
        # 注销客户端
        self.manager.unregister_client('test_client')
        
        # 再次注册应该成功
        success = self.manager.register_client('test_client')
        self.assertTrue(success)
    
    def test_message_frame_creation(self):
        """测试消息帧创建"""
        frame = MessageFrame(command=0x01, data=b'\x00')
        self.assertEqual(frame.command, 0x01)
        self.assertEqual(frame.data, b'\x00')
        self.assertGreater(frame.timestamp, 0)


class TestPowerController(unittest.TestCase):
    """电源控制器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.controller = N100PowerController(port='/dev/null', use_manager=False)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.controller.port, '/dev/null')
        self.assertEqual(self.controller.max_retries, 10)
        self.assertFalse(self.controller.is_connected)
    
    @patch('serial.Serial')
    def test_connection(self, mock_serial):
        """测试连接"""
        # 模拟成功连接
        mock_serial.return_value.is_open = True
        
        success = self.controller.connect()
        self.assertTrue(success)
        self.assertTrue(self.controller.is_connected)


class TestShutdownDaemon(unittest.TestCase):
    """关机守护进程测试类"""
    
    def test_initialization(self):
        """测试初始化"""
        daemon = N100ShutdownDaemon(port='/dev/null')
        self.assertEqual(daemon.port, '/dev/null')
        self.assertFalse(daemon.running)
        self.assertFalse(daemon.shutdown_requested)


class TestIntegration(unittest.TestCase):
    """集成测试类"""
    
    def test_multiple_clients(self):
        """测试多客户端场景"""
        manager = SerialManager(port='/dev/null')
        
        # 注册多个客户端
        clients = ['client1', 'client2', 'client3']
        for client_id in clients:
            success = manager.register_client(client_id)
            self.assertTrue(success)
        
        # 验证客户端数量
        self.assertEqual(len(manager._clients), 3)
        
        # 注销所有客户端
        for client_id in clients:
            manager.unregister_client(client_id)
        
        # 验证客户端已清空
        self.assertEqual(len(manager._clients), 0)
    
    def test_protocol_compatibility(self):
        """测试协议兼容性"""
        # 创建标准帧
        frames = [
            create_led_mode_frame(LEDMode.NORMAL),
            create_led_mode_frame(LEDMode.BREATH),
            create_breath_period_frame(BreathPeriod.PERIOD_1S),
            create_breath_period_frame(BreathPeriod.PERIOD_3S),
            create_breath_period_frame(BreathPeriod.PERIOD_5S),
            create_shutdown_success_frame(),
            create_ack_frame()
        ]
        
        # 验证所有帧都能正确序列化和反序列化
        for frame in frames:
            frame_bytes = frame.to_bytes()
            self.assertTrue(validate_frame(frame_bytes))
            
            parsed_frame = ProtocolFrame.from_bytes(frame_bytes)
            self.assertIsNotNone(parsed_frame)
            self.assertEqual(parsed_frame.command, frame.command)
            self.assertEqual(parsed_frame.data, frame.data)


def run_performance_test():
    """运行性能测试"""
    print("\n=== 性能测试 ===")
    
    # 测试帧创建性能
    start_time = time.time()
    for _ in range(1000):
        frame = create_led_mode_frame(LEDMode.BREATH)
        frame_bytes = frame.to_bytes()
    end_time = time.time()
    
    print(f"帧创建性能: 1000次操作耗时 {end_time - start_time:.3f} 秒")
    
    # 测试帧验证性能
    test_frame = bytes([0xAA, 0x02, 0x01, 0x01, 0xFE, 0x55])
    start_time = time.time()
    for _ in range(1000):
        validate_frame(test_frame)
    end_time = time.time()
    
    print(f"帧验证性能: 1000次操作耗时 {end_time - start_time:.3f} 秒")


def run_stress_test():
    """运行压力测试"""
    print("\n=== 压力测试 ===")
    
    manager = SerialManager(port='/dev/null')
    
    # 测试大量客户端注册
    start_time = time.time()
    clients = []
    for i in range(100):
        client_id = f"stress_client_{i}"
        success = manager.register_client(client_id)
        if success:
            clients.append(client_id)
    end_time = time.time()
    
    print(f"客户端注册压力测试: 100个客户端注册耗时 {end_time - start_time:.3f} 秒")
    print(f"成功注册客户端数量: {len(clients)}")
    
    # 清理
    for client_id in clients:
        manager.unregister_client(client_id)


def main():
    """主函数"""
    print("N100电源控制系统优化版本测试")
    print("=" * 50)
    
    # 运行单元测试
    print("\n=== 单元测试 ===")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能测试
    run_performance_test()
    
    # 运行压力测试
    run_stress_test()
    
    print("\n=== 测试完成 ===")
    print("如果所有测试都通过，说明优化后的系统工作正常")


if __name__ == "__main__":
    main()
