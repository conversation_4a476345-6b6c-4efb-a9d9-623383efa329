#!/bin/bash

# N100代理架构安装脚本
# 安装串口代理服务和关机守护进程

set -e

echo "=== N100代理架构安装脚本 ==="

# 检查权限
if [[ $EUID -ne 0 ]]; then
   echo "错误: 此脚本需要root权限运行"
   echo "请使用: sudo $0"
   exit 1
fi

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "项目根目录: $PROJECT_ROOT"

# 1. 停止现有服务
echo "1. 停止现有服务..."
systemctl stop n100-shutdown 2>/dev/null || true
systemctl stop n100-serial-manager 2>/dev/null || true
systemctl stop n100-shutdown-direct 2>/dev/null || true
systemctl stop n100-serial-proxy 2>/dev/null || true
systemctl stop n100-shutdown-proxy 2>/dev/null || true

systemctl disable n100-shutdown 2>/dev/null || true
systemctl disable n100-serial-manager 2>/dev/null || true
systemctl disable n100-shutdown-direct 2>/dev/null || true
systemctl disable n100-serial-proxy 2>/dev/null || true
systemctl disable n100-shutdown-proxy 2>/dev/null || true

# 2. 创建安装目录
echo "2. 创建安装目录..."
mkdir -p /opt/n100/ctrl
mkdir -p /var/log
mkdir -p /tmp

# 3. 复制源代码
echo "3. 复制源代码..."
cp -r "$PROJECT_ROOT/src"/* /opt/n100/ctrl/
chmod +x /opt/n100/ctrl/*.py

# 4. 设置串口权限
echo "4. 设置串口权限..."
if [ -e /dev/ttyS4 ]; then
    chmod 666 /dev/ttyS4
    echo "串口权限已设置"
else
    echo "警告: /dev/ttyS4 不存在，请检查硬件连接"
fi

# 5. 安装串口代理服务
echo "5. 安装串口代理服务..."
cp "$PROJECT_ROOT/services/n100-serial-proxy.service" /etc/systemd/system/
systemctl daemon-reload
systemctl enable n100-serial-proxy

# 6. 创建代理版关机服务
echo "6. 创建代理版关机服务..."
cat > /etc/systemd/system/n100-shutdown-proxy.service << 'EOF'
[Unit]
Description=N100 Shutdown Daemon (Proxy Version)
Documentation=man:n100-shutdown-proxy(8)
After=n100-serial-proxy.service
Wants=n100-serial-proxy.service
Requires=n100-serial-proxy.service

[Service]
Type=simple
ExecStart=/usr/bin/python3 /opt/n100/ctrl/n100_shutdown_daemon_proxy.py --socket /tmp/n100_serial_proxy.sock --log /var/log/n100_shutdown_proxy.log
Restart=always
RestartSec=5
User=root
Group=root

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=n100-shutdown-proxy

# 环境变量
Environment=PYTHONPATH=/opt/n100/ctrl
Environment=PYTHONUNBUFFERED=1

# 安全配置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/tmp /var/log

# 资源限制
MemoryMax=30M
CPUQuota=5%

[Install]
WantedBy=multi-user.target
EOF

systemctl enable n100-shutdown-proxy

# 7. 安装Python依赖
echo "7. 安装Python依赖..."
if command -v pip3 &> /dev/null; then
    pip3 install pyserial
else
    echo "警告: pip3 未找到，请手动安装 pyserial"
fi

# 8. 创建测试脚本
echo "8. 创建测试脚本..."
cat > /usr/local/bin/n100-test-proxy << 'EOF'
#!/bin/bash
# N100代理架构测试脚本

echo "=== N100代理架构测试 ==="

# 检查服务状态
echo "1. 检查服务状态:"
systemctl is-active n100-serial-proxy && echo "  ✅ 串口代理服务运行正常" || echo "  ❌ 串口代理服务异常"
systemctl is-active n100-shutdown-proxy && echo "  ✅ 关机代理服务运行正常" || echo "  ❌ 关机代理服务异常"

# 检查串口
echo "2. 检查串口:"
if [ -e /dev/ttyS4 ]; then
    echo "  ✅ 串口设备存在"
    ls -l /dev/ttyS4
else
    echo "  ❌ 串口设备不存在"
fi

# 检查Socket
echo "3. 检查代理Socket:"
if [ -e /tmp/n100_serial_proxy.sock ]; then
    echo "  ✅ 代理Socket存在"
    ls -l /tmp/n100_serial_proxy.sock
else
    echo "  ❌ 代理Socket不存在"
fi

# 检查进程
echo "4. 检查进程:"
if pgrep -f "serial_proxy_daemon" > /dev/null; then
    echo "  ✅ 串口代理守护进程运行中"
else
    echo "  ❌ 串口代理守护进程未运行"
fi

if pgrep -f "n100_shutdown_daemon_proxy" > /dev/null; then
    echo "  ✅ 关机代理守护进程运行中"
else
    echo "  ❌ 关机代理守护进程未运行"
fi

# 测试电源控制
echo "5. 测试电源控制:"
cd /opt/n100/ctrl
python3 power_ctrl_cli.py --use-proxy led normal && echo "  ✅ LED控制测试成功" || echo "  ❌ LED控制测试失败"

# 测试关机请求
echo "6. 发送测试关机请求:"
echo -ne '\xAA\x01\x13\xED\x55' | sudo tee /dev/ttyS4 > /dev/null
echo "  ✅ 关机请求已发送"

# 查看日志
echo "7. 最新日志:"
echo "串口代理日志:"
journalctl -u n100-serial-proxy -n 5 --no-pager
echo "关机代理日志:"
journalctl -u n100-shutdown-proxy -n 5 --no-pager

echo "=== 测试完成 ==="
EOF

chmod +x /usr/local/bin/n100-test-proxy

# 9. 启动服务
echo "9. 启动服务..."
systemctl start n100-serial-proxy
sleep 2
systemctl start n100-shutdown-proxy

# 10. 检查服务状态
echo "10. 检查服务状态..."
echo "串口代理服务:"
systemctl status n100-serial-proxy --no-pager -l
echo ""
echo "关机代理服务:"
systemctl status n100-shutdown-proxy --no-pager -l

# 11. 显示安装结果
echo ""
echo "=== 安装完成 ==="
echo "✅ 代理架构已安装"
echo ""
echo "服务状态:"
systemctl is-active n100-serial-proxy && echo "  ✅ n100-serial-proxy: 运行中" || echo "  ❌ n100-serial-proxy: 异常"
systemctl is-active n100-shutdown-proxy && echo "  ✅ n100-shutdown-proxy: 运行中" || echo "  ❌ n100-shutdown-proxy: 异常"

echo ""
echo "使用方法:"
echo "  测试系统: n100-test-proxy"
echo "  电源控制: cd /opt/n100/ctrl && python3 power_ctrl_cli.py --use-proxy led normal"
echo "  模拟关机请求: echo -ne '\\xAA\\x01\\x13\\xED\\x55' | sudo tee /dev/ttyS4"

echo ""
echo "日志查看:"
echo "  串口代理: journalctl -u n100-serial-proxy -f"
echo "  关机代理: journalctl -u n100-shutdown-proxy -f"

echo ""
echo "🎉 安装成功！代理架构已启动。"
