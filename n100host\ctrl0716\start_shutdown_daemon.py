#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
手动启动关机守护进程脚本
用于测试和调试关机守护进程
"""

import os
import sys
import signal
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

try:
    from n100_shutdown_daemon_proxy import N100ShutdownDaemonProxy
except ImportError as e:
    print(f"错误: 无法导入关机守护进程模块: {e}")
    sys.exit(1)


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n收到信号 {signum}，正在停止...")
    sys.exit(0)


def main():
    """主函数"""
    print("手动启动N100关机守护进程")
    print("=" * 40)
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 创建关机守护进程
        daemon = N100ShutdownDaemonProxy()
        
        print("启动关机守护进程...")
        daemon.start()
        
        print("关机守护进程已启动，按 Ctrl+C 停止")
        print("等待关机请求...")
        
        # 保持运行
        while True:
            time.sleep(1)
    
    except KeyboardInterrupt:
        print("\n用户中断，正在停止...")
    
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        try:
            if 'daemon' in locals():
                daemon.stop()
                print("关机守护进程已停止")
        except:
            pass
    
    return 0


if __name__ == "__main__":
    exit(main())
