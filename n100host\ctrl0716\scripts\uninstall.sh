#!/bin/bash

# N100系统卸载脚本
# 卸载所有N100相关服务和文件

set -e

echo "=== N100系统卸载脚本 ==="

# 检查权限
if [[ $EUID -ne 0 ]]; then
   echo "错误: 此脚本需要root权限运行"
   echo "请使用: sudo $0"
   exit 1
fi

# 1. 停止所有服务
echo "1. 停止所有N100服务..."
services=(
    "n100-shutdown"
    "n100-serial-manager"
    "n100-serial-proxy"
    "n100-shutdown-proxy"
    "n100-shutdown-direct"
)

for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service" 2>/dev/null; then
        echo "  停止服务: $service"
        systemctl stop "$service"
    fi
    
    if systemctl is-enabled --quiet "$service" 2>/dev/null; then
        echo "  禁用服务: $service"
        systemctl disable "$service"
    fi
done

# 2. 删除服务文件
echo "2. 删除服务文件..."
service_files=(
    "/etc/systemd/system/n100-shutdown.service"
    "/etc/systemd/system/n100-serial-manager.service"
    "/etc/systemd/system/n100-serial-proxy.service"
    "/etc/systemd/system/n100-shutdown-proxy.service"
    "/etc/systemd/system/n100-shutdown-direct.service"
)

for file in "${service_files[@]}"; do
    if [ -f "$file" ]; then
        echo "  删除: $file"
        rm -f "$file"
    fi
done

# 重新加载systemd
systemctl daemon-reload

# 3. 删除安装目录
echo "3. 删除安装目录..."
if [ -d "/opt/n100" ]; then
    echo "  删除: /opt/n100"
    rm -rf /opt/n100
fi

# 4. 删除测试脚本
echo "4. 删除测试脚本..."
test_scripts=(
    "/usr/local/bin/n100-test-proxy"
    "/usr/local/bin/n100-test-direct"
    "/usr/local/bin/n100-test-shutdown"
)

for script in "${test_scripts[@]}"; do
    if [ -f "$script" ]; then
        echo "  删除: $script"
        rm -f "$script"
    fi
done

# 5. 清理临时文件
echo "5. 清理临时文件..."
temp_files=(
    "/tmp/n100_serial_proxy.sock"
    "/tmp/n100_*.log"
)

for pattern in "${temp_files[@]}"; do
    if ls $pattern 1> /dev/null 2>&1; then
        echo "  删除: $pattern"
        rm -f $pattern
    fi
done

# 6. 清理日志文件（可选）
echo "6. 清理日志文件..."
read -p "是否删除日志文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_files=(
        "/var/log/n100_*.log"
    )
    
    for pattern in "${log_files[@]}"; do
        if ls $pattern 1> /dev/null 2>&1; then
            echo "  删除: $pattern"
            rm -f $pattern
        fi
    done
    
    # 清理journalctl日志
    echo "  清理systemd日志..."
    journalctl --vacuum-time=1d --quiet || true
fi

# 7. 显示卸载结果
echo ""
echo "=== 卸载完成 ==="
echo "✅ 所有N100服务已停止并删除"
echo "✅ 安装文件已清理"
echo "✅ 测试脚本已删除"

# 检查是否还有残留进程
echo ""
echo "检查残留进程:"
if pgrep -f "n100" > /dev/null; then
    echo "  ⚠️  发现残留进程:"
    pgrep -f "n100" -l
    echo "  请手动终止这些进程"
else
    echo "  ✅ 没有残留进程"
fi

# 检查是否还有残留文件
echo ""
echo "检查残留文件:"
remaining_files=()

if [ -d "/opt/n100" ]; then
    remaining_files+=("/opt/n100")
fi

if ls /etc/systemd/system/n100-*.service 1> /dev/null 2>&1; then
    remaining_files+=($(ls /etc/systemd/system/n100-*.service))
fi

if [ ${#remaining_files[@]} -gt 0 ]; then
    echo "  ⚠️  发现残留文件:"
    for file in "${remaining_files[@]}"; do
        echo "    $file"
    done
    echo "  请手动删除这些文件"
else
    echo "  ✅ 没有残留文件"
fi

echo ""
echo "注意事项:"
echo "  - 串口设备 /dev/ttyS4 权限未修改"
echo "  - Python依赖包 pyserial 未卸载"
echo "  - 如需完全清理，请手动处理上述项目"

echo ""
echo "🎉 卸载完成！"
