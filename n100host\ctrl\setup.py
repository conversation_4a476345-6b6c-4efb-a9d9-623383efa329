#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制系统安装脚本
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    with open("README.md", "r", encoding="utf-8") as f:
        return f.read()

# 读取requirements
def read_requirements():
    requirements = []
    if os.path.exists("requirements.txt"):
        with open("requirements.txt", "r", encoding="utf-8") as f:
            requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]
    return requirements

setup(
    name="n100-power-control",
    version="1.0.0",
    author="N100 Team",
    author_email="<EMAIL>",
    description="N100电源控制系统",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/n100/power-control",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: POSIX :: Linux",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: System :: Hardware",
        "Topic :: System :: Power (UPS)",
    ],
    python_requires=">=3.6",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
        ],
    },
    entry_points={
        "console_scripts": [
            "n100-power-ctrl=src.power_ctrl_cli:main",
            "n100-shutdown-daemon=src.n100_shutdown_daemon:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.sh", "*.service"],
    },
    data_files=[
        ("share/n100/scripts", ["scripts/install_shutdown_system.sh", "scripts/n100-shutdown-hook.sh"]),
        ("share/n100/services", ["services/n100-shutdown.service"]),
        ("share/n100/docs", ["docs/README.md", "docs/USAGE.md", "docs/SHUTDOWN_SYSTEM.md"]),
    ],
)
