#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制系统协议测试
测试协议模块的各种功能
"""

import unittest
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))

from protocol import (
    ProtocolFrame, MessageType, LEDMode, BreathPeriod,
    calculate_checksum, create_frame, validate_frame,
    create_led_mode_frame, create_breath_period_frame,
    create_shutdown_success_frame, create_ack_frame,
    parse_frame, get_frame_description,
    STANDARD_FRAMES, get_standard_frame
)


class TestProtocolBasics(unittest.TestCase):
    """协议基础功能测试"""
    
    def test_checksum_calculation(self):
        """测试校验和计算"""
        # 测试用例：(输入数据, 期望校验和)
        test_cases = [
            (bytes([0x01, 0x00]), 0xFF),  # LED正常模式
            (bytes([0x01, 0x01]), 0xFE),  # LED呼吸模式
            (bytes([0x02, 0x01]), 0xFD),  # 1秒呼吸周期
            (bytes([0x02, 0x03]), 0xFB),  # 3秒呼吸周期
            (bytes([0x02, 0x05]), 0xF9),  # 5秒呼吸周期
            (bytes([0x03]), 0xFD),        # 关机成功
            (bytes([0x80]), 0x80),        # ACK应答
        ]
        
        for data, expected in test_cases:
            with self.subTest(data=data.hex()):
                result = calculate_checksum(data)
                self.assertEqual(result, expected, 
                    f"校验和计算错误: 数据={data.hex()}, 期望=0x{expected:02X}, 实际=0x{result:02X}")
    
    def test_frame_creation(self):
        """测试帧创建"""
        # 测试LED模式帧
        led_normal_frame = create_frame(MessageType.LED_MODE, bytes([LEDMode.NORMAL]))
        self.assertEqual(led_normal_frame.command, MessageType.LED_MODE)
        self.assertEqual(led_normal_frame.data, bytes([LEDMode.NORMAL]))
        self.assertEqual(led_normal_frame.length, 2)  # 命令(1) + 数据(1)
        
        # 测试关机成功帧（无数据）
        shutdown_frame = create_frame(MessageType.SHUTDOWN_SUCCESS)
        self.assertEqual(shutdown_frame.command, MessageType.SHUTDOWN_SUCCESS)
        self.assertEqual(shutdown_frame.data, b'')
        self.assertEqual(shutdown_frame.length, 1)  # 只有命令(1)
    
    def test_frame_validation(self):
        """测试帧验证"""
        # 有效帧
        valid_frames = [
            bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55]),  # LED正常
            bytes([0xAA, 0x02, 0x01, 0x01, 0xFE, 0x55]),  # LED呼吸
            bytes([0xAA, 0x01, 0x03, 0xFD, 0x55]),        # 关机成功
            bytes([0xAA, 0x01, 0x80, 0x80, 0x55]),        # ACK应答
        ]
        
        for frame in valid_frames:
            with self.subTest(frame=frame.hex()):
                self.assertTrue(validate_frame(frame), f"有效帧验证失败: {frame.hex()}")
        
        # 无效帧
        invalid_frames = [
            bytes([0xBB, 0x02, 0x01, 0x00, 0xFF, 0x55]),  # 错误帧头
            bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x44]),  # 错误帧尾
            bytes([0xAA, 0x02, 0x01, 0x00, 0xFE, 0x55]),  # 错误校验和
            bytes([0xAA, 0x02, 0x01]),                     # 帧太短
        ]
        
        for frame in invalid_frames:
            with self.subTest(frame=frame.hex()):
                self.assertFalse(validate_frame(frame), f"无效帧验证通过: {frame.hex()}")


class TestProtocolFrames(unittest.TestCase):
    """协议帧测试"""
    
    def test_protocol_frame_to_bytes(self):
        """测试协议帧转换为字节"""
        frame = ProtocolFrame(
            length=2,
            command=MessageType.LED_MODE,
            data=bytes([LEDMode.NORMAL]),
            checksum=0xFF
        )
        
        expected = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
        result = frame.to_bytes()
        self.assertEqual(result, expected)
    
    def test_protocol_frame_from_bytes(self):
        """测试从字节创建协议帧"""
        frame_data = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
        frame = ProtocolFrame.from_bytes(frame_data)
        
        self.assertIsNotNone(frame)
        self.assertEqual(frame.command, MessageType.LED_MODE)
        self.assertEqual(frame.data, bytes([LEDMode.NORMAL]))
        self.assertEqual(frame.checksum, 0xFF)
    
    def test_predefined_frame_creators(self):
        """测试预定义帧创建函数"""
        # LED模式帧
        led_normal = create_led_mode_frame(LEDMode.NORMAL)
        self.assertEqual(led_normal.command, MessageType.LED_MODE)
        self.assertEqual(led_normal.data, bytes([LEDMode.NORMAL]))
        
        led_breath = create_led_mode_frame(LEDMode.BREATH)
        self.assertEqual(led_breath.command, MessageType.LED_MODE)
        self.assertEqual(led_breath.data, bytes([LEDMode.BREATH]))
        
        # 呼吸周期帧
        breath_3s = create_breath_period_frame(BreathPeriod.PERIOD_3S)
        self.assertEqual(breath_3s.command, MessageType.BREATH_PERIOD)
        self.assertEqual(breath_3s.data, bytes([BreathPeriod.PERIOD_3S]))
        
        # 关机成功帧
        shutdown = create_shutdown_success_frame()
        self.assertEqual(shutdown.command, MessageType.SHUTDOWN_SUCCESS)
        self.assertEqual(shutdown.data, b'')
        
        # ACK帧
        ack = create_ack_frame()
        self.assertEqual(ack.command, MessageType.ACK)
        self.assertEqual(ack.data, b'')


class TestProtocolParsing(unittest.TestCase):
    """协议解析测试"""
    
    def test_frame_parsing(self):
        """测试帧解析"""
        test_frames = [
            (bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55]), MessageType.LED_MODE, bytes([0x00])),
            (bytes([0xAA, 0x02, 0x01, 0x01, 0xFE, 0x55]), MessageType.LED_MODE, bytes([0x01])),
            (bytes([0xAA, 0x02, 0x02, 0x03, 0xFB, 0x55]), MessageType.BREATH_PERIOD, bytes([0x03])),
            (bytes([0xAA, 0x01, 0x03, 0xFD, 0x55]), MessageType.SHUTDOWN_SUCCESS, b''),
            (bytes([0xAA, 0x01, 0x80, 0x80, 0x55]), MessageType.ACK, b''),
        ]
        
        for frame_data, expected_command, expected_data in test_frames:
            with self.subTest(frame=frame_data.hex()):
                valid, command, data = parse_frame(frame_data)
                self.assertTrue(valid, f"帧解析失败: {frame_data.hex()}")
                self.assertEqual(command, expected_command)
                self.assertEqual(data, expected_data)
    
    def test_frame_description(self):
        """测试帧描述"""
        test_cases = [
            (MessageType.LED_MODE, bytes([LEDMode.NORMAL]), "LED模式设置 - 正常模式"),
            (MessageType.LED_MODE, bytes([LEDMode.BREATH]), "LED模式设置 - 呼吸模式"),
            (MessageType.BREATH_PERIOD, bytes([3]), "呼吸周期设置 - 3秒"),
            (MessageType.SHUTDOWN_SUCCESS, b'', "关机成功消息"),
            (MessageType.ACK, b'', "ACK应答"),
            (0xFF, b'', "未知命令(0xFF)"),
        ]
        
        for command, data, expected_desc in test_cases:
            with self.subTest(command=command):
                result = get_frame_description(command, data)
                self.assertEqual(result, expected_desc)


class TestStandardFrames(unittest.TestCase):
    """标准帧测试"""
    
    def test_standard_frames_validity(self):
        """测试标准帧有效性"""
        for name, frame_data in STANDARD_FRAMES.items():
            with self.subTest(frame=name):
                self.assertTrue(validate_frame(frame_data), 
                    f"标准帧无效: {name} = {frame_data.hex()}")
    
    def test_get_standard_frame(self):
        """测试获取标准帧"""
        # 测试存在的帧
        led_normal = get_standard_frame('led_normal')
        self.assertIsNotNone(led_normal)
        self.assertEqual(led_normal, STANDARD_FRAMES['led_normal'])
        
        # 测试不存在的帧
        invalid_frame = get_standard_frame('invalid_frame')
        self.assertIsNone(invalid_frame)
    
    def test_created_frames_match_standard(self):
        """测试创建的帧与标准帧匹配"""
        test_cases = [
            ('led_normal', create_led_mode_frame(LEDMode.NORMAL)),
            ('led_breath', create_led_mode_frame(LEDMode.BREATH)),
            ('breath_1s', create_breath_period_frame(BreathPeriod.PERIOD_1S)),
            ('breath_3s', create_breath_period_frame(BreathPeriod.PERIOD_3S)),
            ('breath_5s', create_breath_period_frame(BreathPeriod.PERIOD_5S)),
            ('shutdown_success', create_shutdown_success_frame()),
            ('ack', create_ack_frame()),
        ]
        
        for std_name, created_frame in test_cases:
            with self.subTest(frame=std_name):
                std_frame = get_standard_frame(std_name)
                created_bytes = created_frame.to_bytes()
                self.assertEqual(created_bytes, std_frame, 
                    f"创建的帧与标准帧不匹配: {std_name}")


class TestProtocolEdgeCases(unittest.TestCase):
    """协议边界情况测试"""
    
    def test_empty_data_frame(self):
        """测试空数据帧"""
        frame = create_frame(MessageType.SHUTDOWN_SUCCESS)
        self.assertEqual(frame.data, b'')
        self.assertEqual(frame.length, 1)
        
        frame_bytes = frame.to_bytes()
        self.assertTrue(validate_frame(frame_bytes))
    
    def test_max_data_length(self):
        """测试最大数据长度"""
        from protocol import PROTOCOL_DATA_MAX
        
        # 测试最大长度数据
        max_data = b'\x00' * PROTOCOL_DATA_MAX
        frame = create_frame(0x99, max_data)
        self.assertEqual(len(frame.data), PROTOCOL_DATA_MAX)
        
        # 测试超过最大长度
        with self.assertRaises(ValueError):
            oversized_data = b'\x00' * (PROTOCOL_DATA_MAX + 1)
            create_frame(0x99, oversized_data)
    
    def test_invalid_frame_data(self):
        """测试无效帧数据"""
        invalid_cases = [
            b'',  # 空数据
            b'\xAA',  # 只有帧头
            b'\xAA\x02',  # 缺少数据
            b'\xAA\x02\x01\x00\xFF',  # 缺少帧尾
        ]
        
        for invalid_data in invalid_cases:
            with self.subTest(data=invalid_data.hex()):
                self.assertFalse(validate_frame(invalid_data))
                frame = ProtocolFrame.from_bytes(invalid_data)
                self.assertIsNone(frame)


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestProtocolBasics,
        TestProtocolFrames,
        TestProtocolParsing,
        TestStandardFrames,
        TestProtocolEdgeCases,
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    print("N100电源控制系统协议测试")
    print("=" * 50)
    
    success = run_tests()
    
    if success:
        print("\n🎉 所有协议测试通过！")
        exit(0)
    else:
        print("\n❌ 部分协议测试失败！")
        exit(1)
