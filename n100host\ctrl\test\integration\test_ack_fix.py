#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试ACK修复
验证串口管理器的ACK等待机制是否修复
"""

import os
import sys
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from memory_serial_simulator import MemorySerial, PowerBoardSimulator
    from serial_manager import SerialManager, MessageType
    from n100_power_ctrl import N100PowerController, LEDMode
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    sys.exit(1)


def test_ack_fix():
    """测试ACK修复"""
    print("=== 测试ACK修复 ===")
    
    # 创建内存串口对
    n100_serial = MemorySerial("N100")
    power_serial = MemorySerial("PowerBoard")
    n100_serial.connect_peer(power_serial)
    
    # 创建电源板模拟器
    power_simulator = PowerBoardSimulator(power_serial)
    power_simulator.start()
    time.sleep(0.5)
    
    try:
        # 创建串口管理器
        manager = SerialManager()
        manager.serial = n100_serial
        manager.is_running = True

        # 启动读取线程
        import threading
        manager._read_thread = threading.Thread(target=manager._read_loop, daemon=True)
        manager._read_thread.start()
        
        # 注册客户端
        client_id = "test_client"
        
        def on_message(message):
            print(f"[客户端] 收到消息: 命令=0x{message.command:02X}")
        
        def on_error(error):
            print(f"[客户端] 错误: {error}")
        
        success = manager.register_client(client_id, on_message, on_error)
        print(f"客户端注册: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            # 发送LED命令
            print("\n发送LED正常模式命令...")
            success = manager.send_message(
                client_id=client_id,
                command=0x01,  # LED命令
                data=bytes([0x00]),  # 正常模式
                max_retries=3
            )
            print(f"LED命令发送: {'✅ 成功' if success else '❌ 失败'}")
            
            time.sleep(1)
            
            # 发送LED呼吸模式命令
            print("\n发送LED呼吸模式命令...")
            success = manager.send_message(
                client_id=client_id,
                command=0x01,  # LED命令
                data=bytes([0x01]),  # 呼吸模式
                max_retries=3
            )
            print(f"LED呼吸命令发送: {'✅ 成功' if success else '❌ 失败'}")
            
            manager.unregister_client(client_id)
        
        # 显示统计信息
        print(f"\n=== 通信统计 ===")
        print(f"电源板接收命令: {power_simulator.stats['received_commands']}")
        print(f"电源板发送ACK: {power_simulator.stats['sent_acks']}")
        print(f"N100发送字节: {n100_serial.bytes_sent}")
        print(f"N100接收字节: {n100_serial.bytes_received}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        power_simulator.stop()


def test_power_controller_with_manager():
    """测试电源控制器使用串口管理器"""
    print("\n=== 测试电源控制器使用串口管理器 ===")
    
    # 创建内存串口对
    n100_serial = MemorySerial("N100")
    power_serial = MemorySerial("PowerBoard")
    n100_serial.connect_peer(power_serial)
    
    # 创建电源板模拟器
    power_simulator = PowerBoardSimulator(power_serial)
    power_simulator.start()
    time.sleep(0.5)
    
    try:
        # 创建电源控制器（使用串口管理器）
        controller = N100PowerController(use_manager=True, max_retries=3, timeout=2.0)
        
        # 替换串口管理器的串口
        from serial_manager import get_serial_manager
        manager = get_serial_manager()
        manager.serial = n100_serial
        manager.is_running = True

        # 启动读取线程
        import threading
        manager._read_thread = threading.Thread(target=manager._read_loop, daemon=True)
        manager._read_thread.start()
        
        # 连接控制器
        if controller.connect():
            print("✅ 控制器连接成功")
            
            # 测试LED控制
            print("\n1. 测试LED正常模式...")
            success = controller.set_led_mode(LEDMode.NORMAL)
            print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
            
            if success:
                time.sleep(0.5)
                
                print("\n2. 测试LED呼吸模式...")
                success = controller.set_led_mode(LEDMode.BREATH)
                print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
            
            controller.disconnect()
            return success
        else:
            print("❌ 控制器连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 电源控制器测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        power_simulator.stop()


def main():
    """主函数"""
    print("ACK修复验证测试")
    print("=" * 30)
    
    # 1. 测试串口管理器ACK修复
    manager_ok = test_ack_fix()
    
    # 2. 测试电源控制器使用串口管理器
    controller_ok = test_power_controller_with_manager()
    
    # 显示结果
    print(f"\n=== 测试结果 ===")
    print(f"串口管理器ACK: {'✅ 修复成功' if manager_ok else '❌ 仍有问题'}")
    print(f"电源控制器: {'✅ 正常' if controller_ok else '❌ 异常'}")
    
    if manager_ok and controller_ok:
        print("\n🎉 ACK修复验证通过！")
        print("现在可以正常使用串口管理器模式了")
    else:
        print("\n❌ ACK修复验证失败")
        print("建议继续使用 --no-manager 模式")
    
    return 0 if (manager_ok and controller_ok) else 1


if __name__ == "__main__":
    sys.exit(main())
