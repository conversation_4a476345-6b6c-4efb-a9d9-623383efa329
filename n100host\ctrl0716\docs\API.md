# N100电源控制系统API参考

## 概述

N100电源控制系统提供了完整的Python API，用于控制电源板的LED模式、呼吸灯周期和关机管理。本文档详细描述了所有可用的类、方法和函数。

## 核心类

### N100PowerController

主要的电源控制器类，提供所有电源控制功能。

#### 构造函数

```python
N100PowerController(port='/dev/ttyS4', baudrate=115200, timeout=1.0, 
                   max_retries=10, use_manager=True, use_proxy=False)
```

**参数**:
- `port` (str): 串口设备路径，默认 '/dev/ttyS4'
- `baudrate` (int): 波特率，默认 115200
- `timeout` (float): 读取超时时间(秒)，默认 1.0
- `max_retries` (int): 最大重试次数，默认 10
- `use_manager` (bool): 是否使用串口管理器，默认 True
- `use_proxy` (bool): 是否使用串口代理，默认 False

**示例**:
```python
# 基本用法
controller = N100PowerController()

# 自定义参数
controller = N100PowerController(
    port='/dev/ttyS4',
    baudrate=115200,
    timeout=2.0,
    max_retries=5
)

# 使用代理模式
controller = N100PowerController(use_proxy=True)
```

#### 连接管理方法

##### connect()

连接到串口或代理服务。

```python
def connect() -> bool
```

**返回值**:
- `bool`: 连接是否成功

**示例**:
```python
if controller.connect():
    print("连接成功")
else:
    print("连接失败")
```

##### disconnect()

断开连接。

```python
def disconnect() -> None
```

**示例**:
```python
controller.disconnect()
```

##### is_port_connected()

检查连接状态。

```python
def is_port_connected() -> bool
```

**返回值**:
- `bool`: 是否已连接

**示例**:
```python
if controller.is_port_connected():
    print("已连接")
```

##### get_port_info()

获取端口信息。

```python
def get_port_info() -> dict
```

**返回值**:
- `dict`: 包含端口信息的字典

**示例**:
```python
info = controller.get_port_info()
print(f"端口: {info['port']}")
print(f"波特率: {info['baudrate']}")
print(f"连接状态: {info['connected']}")
```

#### LED控制方法

##### set_led_mode(mode)

设置LED模式。

```python
def set_led_mode(mode: LEDMode) -> bool
```

**参数**:
- `mode` (LEDMode): LED模式枚举值

**返回值**:
- `bool`: 设置是否成功

**示例**:
```python
# 使用枚举
success = controller.set_led_mode(LEDMode.NORMAL)
success = controller.set_led_mode(LEDMode.BREATH)
```

##### set_led_normal()

设置LED为正常模式（便捷方法）。

```python
def set_led_normal() -> bool
```

**返回值**:
- `bool`: 设置是否成功

**示例**:
```python
if controller.set_led_normal():
    print("LED正常模式设置成功")
```

##### set_led_breath()

设置LED为呼吸模式（便捷方法）。

```python
def set_led_breath() -> bool
```

**返回值**:
- `bool`: 设置是否成功

**示例**:
```python
if controller.set_led_breath():
    print("LED呼吸模式设置成功")
```

#### 呼吸灯控制方法

##### set_breath_period(period)

设置呼吸灯周期。

```python
def set_breath_period(period: BreathPeriod) -> bool
```

**参数**:
- `period` (BreathPeriod): 呼吸周期枚举值

**返回值**:
- `bool`: 设置是否成功

**示例**:
```python
# 使用枚举
success = controller.set_breath_period(BreathPeriod.PERIOD_3S)
```

##### set_breath_1s(), set_breath_3s(), set_breath_5s()

设置特定呼吸周期（便捷方法）。

```python
def set_breath_1s() -> bool
def set_breath_3s() -> bool
def set_breath_5s() -> bool
```

**示例**:
```python
controller.set_breath_1s()  # 1秒周期
controller.set_breath_3s()  # 3秒周期
controller.set_breath_5s()  # 5秒周期
```

#### 关机控制方法

##### send_shutdown_success()

发送关机成功消息。

```python
def send_shutdown_success() -> bool
```

**返回值**:
- `bool`: 发送是否成功

**示例**:
```python
if controller.send_shutdown_success():
    print("关机成功消息已发送")
```

#### 自定义命令方法

##### send_custom_command(command, data)

发送自定义命令。

```python
def send_custom_command(command: int, data: bytes = b'') -> bool
```

**参数**:
- `command` (int): 命令字节
- `data` (bytes): 数据字节，默认为空

**返回值**:
- `bool`: 发送是否成功

**示例**:
```python
# 发送自定义LED命令
success = controller.send_custom_command(0x01, b'\x00')

# 发送无数据命令
success = controller.send_custom_command(0x03)
```

#### 回调方法

##### set_ack_callback(callback)

设置ACK接收回调函数。

```python
def set_ack_callback(callback: Callable[[int], None]) -> None
```

**参数**:
- `callback`: 回调函数，接收命令参数

**示例**:
```python
def on_ack_received(command):
    print(f"收到ACK: 0x{command:02X}")

controller.set_ack_callback(on_ack_received)
```

##### set_error_callback(callback)

设置错误回调函数。

```python
def set_error_callback(callback: Callable[[str], None]) -> None
```

**参数**:
- `callback`: 回调函数，接收错误消息参数

**示例**:
```python
def on_error(error_msg):
    print(f"错误: {error_msg}")

controller.set_error_callback(on_error)
```

## 枚举类型

### LEDMode

LED模式枚举。

```python
class LEDMode(IntEnum):
    NORMAL = 0x00  # 正常模式
    BREATH = 0x01  # 呼吸模式
```

**示例**:
```python
from n100_power_ctrl import LEDMode

controller.set_led_mode(LEDMode.NORMAL)
controller.set_led_mode(LEDMode.BREATH)
```

### BreathPeriod

呼吸周期枚举。

```python
class BreathPeriod(IntEnum):
    PERIOD_1S = 1  # 1秒周期
    PERIOD_3S = 3  # 3秒周期
    PERIOD_5S = 5  # 5秒周期
```

**示例**:
```python
from n100_power_ctrl import BreathPeriod

controller.set_breath_period(BreathPeriod.PERIOD_1S)
controller.set_breath_period(BreathPeriod.PERIOD_3S)
controller.set_breath_period(BreathPeriod.PERIOD_5S)
```

### PowerCommand

电源控制命令枚举。

```python
class PowerCommand(IntEnum):
    LED_MODE = 0x01        # LED模式设置命令
    BREATH_PERIOD = 0x02   # 呼吸灯周期设置命令
    SHUTDOWN = 0x03        # 关机成功消息
    SHUTDOWN_REQ = 0x13    # 关机请求命令
    ACK = 0x80            # 通用应答命令
```

## 协议模块

### ProtocolFrame

协议帧数据结构。

```python
@dataclass
class ProtocolFrame:
    header: int = 0xAA
    length: int = 0
    command: int = 0
    data: bytes = b''
    checksum: int = 0
    tail: int = 0x55
```

#### 方法

##### to_bytes()

转换为字节数组。

```python
def to_bytes() -> bytes
```

**示例**:
```python
frame = create_led_mode_frame(LEDMode.NORMAL)
frame_bytes = frame.to_bytes()
print(frame_bytes.hex(' ').upper())  # AA 02 01 00 FF 55
```

##### from_bytes(data)

从字节数组创建协议帧。

```python
@classmethod
def from_bytes(cls, data: bytes) -> Optional['ProtocolFrame']
```

**参数**:
- `data` (bytes): 帧数据

**返回值**:
- `Optional[ProtocolFrame]`: 协议帧对象，失败时返回None

**示例**:
```python
frame_data = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
frame = ProtocolFrame.from_bytes(frame_data)
if frame:
    print(f"命令: 0x{frame.command:02X}")
```

### 协议工具函数

#### calculate_checksum(data)

计算校验和。

```python
def calculate_checksum(data: bytes) -> int
```

**参数**:
- `data` (bytes): 需要计算校验和的数据

**返回值**:
- `int`: 校验和

**示例**:
```python
data = bytes([0x01, 0x00])  # LED正常模式
checksum = calculate_checksum(data)
print(f"校验和: 0x{checksum:02X}")  # 0xFF
```

#### create_frame(command, data)

创建协议帧。

```python
def create_frame(command: int, data: bytes = b'') -> ProtocolFrame
```

**参数**:
- `command` (int): 命令字节
- `data` (bytes): 数据字节

**返回值**:
- `ProtocolFrame`: 协议帧对象

**示例**:
```python
frame = create_frame(0x01, bytes([0x00]))
print(frame.to_bytes().hex(' ').upper())
```

#### validate_frame(frame_data)

验证协议帧。

```python
def validate_frame(frame_data: bytes) -> bool
```

**参数**:
- `frame_data` (bytes): 帧数据

**返回值**:
- `bool`: 是否为有效帧

**示例**:
```python
frame_data = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
if validate_frame(frame_data):
    print("有效帧")
else:
    print("无效帧")
```

#### 预定义帧创建函数

##### create_led_mode_frame(mode)

创建LED模式设置帧。

```python
def create_led_mode_frame(mode: LEDMode) -> ProtocolFrame
```

##### create_breath_period_frame(period)

创建呼吸周期设置帧。

```python
def create_breath_period_frame(period: BreathPeriod) -> ProtocolFrame
```

##### create_shutdown_success_frame()

创建关机成功帧。

```python
def create_shutdown_success_frame() -> ProtocolFrame
```

##### create_ack_frame()

创建ACK应答帧。

```python
def create_ack_frame() -> ProtocolFrame
```

**示例**:
```python
# 创建各种预定义帧
led_frame = create_led_mode_frame(LEDMode.NORMAL)
breath_frame = create_breath_period_frame(BreathPeriod.PERIOD_3S)
shutdown_frame = create_shutdown_success_frame()
ack_frame = create_ack_frame()
```

## 命令行工具

### power_ctrl_cli.py

命令行电源控制工具。

#### 基本语法

```bash
python power_ctrl_cli.py [选项] <命令> [参数]
```

#### 全局选项

- `--port PORT`: 串口设备路径，默认 /dev/ttyS4
- `--baudrate RATE`: 波特率，默认 115200
- `--timeout SECONDS`: 超时时间，默认 1.0
- `--retries COUNT`: 重试次数，默认 10
- `--use-proxy`: 使用串口代理模式
- `--no-manager`: 禁用串口管理器

#### 命令

##### led

设置LED模式。

```bash
python power_ctrl_cli.py led <mode>
```

**参数**:
- `mode`: normal 或 breath

**示例**:
```bash
python power_ctrl_cli.py led normal
python power_ctrl_cli.py led breath
```

##### breath

设置呼吸周期。

```bash
python power_ctrl_cli.py breath <period>
```

**参数**:
- `period`: 1, 3, 或 5 (秒)

**示例**:
```bash
python power_ctrl_cli.py breath 1
python power_ctrl_cli.py breath 3
python power_ctrl_cli.py breath 5
```

##### shutdown

发送关机成功消息。

```bash
python power_ctrl_cli.py shutdown
```

##### custom

发送自定义命令。

```bash
python power_ctrl_cli.py custom <command_hex> [data_hex]
```

**参数**:
- `command_hex`: 命令字节（16进制）
- `data_hex`: 数据字节（16进制，可选）

**示例**:
```bash
python power_ctrl_cli.py custom 01 00  # LED正常模式
python power_ctrl_cli.py custom 03     # 关机成功
```

##### test

运行测试序列。

```bash
python power_ctrl_cli.py test [--delay SECONDS]
```

**选项**:
- `--delay`: 命令间延迟时间，默认 2.0秒

##### status

查询串口状态。

```bash
python power_ctrl_cli.py status
```

#### 使用示例

```bash
# 基本使用
python power_ctrl_cli.py led normal
python power_ctrl_cli.py breath 3
python power_ctrl_cli.py shutdown

# 使用代理模式
python power_ctrl_cli.py --use-proxy led breath

# 自定义串口参数
python power_ctrl_cli.py --port /dev/ttyUSB0 --baudrate 9600 led normal

# 运行测试
python power_ctrl_cli.py test --delay 1.0
```

## 完整使用示例

### 基本使用流程

```python
from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod

# 创建控制器
controller = N100PowerController()

# 设置回调
def on_ack(command):
    print(f"收到ACK: 0x{command:02X}")

def on_error(error):
    print(f"错误: {error}")

controller.set_ack_callback(on_ack)
controller.set_error_callback(on_error)

# 连接并控制
if controller.connect():
    try:
        # 设置LED模式
        controller.set_led_normal()
        time.sleep(1)
        controller.set_led_breath()

        # 设置呼吸周期
        controller.set_breath_3s()

        # 发送关机成功
        controller.send_shutdown_success()

    finally:
        controller.disconnect()
```

### 代理模式使用

```python
# 使用代理模式
controller = N100PowerController(use_proxy=True)

if controller.connect():
    # 所有操作与直接模式相同
    controller.set_led_breath()
    controller.set_breath_5s()
    controller.disconnect()
```

### 错误处理

```python
controller = N100PowerController()

try:
    if not controller.connect():
        raise Exception("无法连接到串口")

    if not controller.set_led_normal():
        raise Exception("LED设置失败")

    print("操作成功")

except Exception as e:
    print(f"操作失败: {e}")

finally:
    controller.disconnect()
```

## 错误代码

| 错误类型 | 描述 | 解决方法 |
|----------|------|----------|
| 连接失败 | 无法打开串口 | 检查串口设备和权限 |
| 超时错误 | 未收到ACK应答 | 检查电源板连接和状态 |
| 校验错误 | 接收到无效帧 | 检查通信质量和干扰 |
| 参数错误 | 无效的参数值 | 检查参数类型和范围 |

---

**版本**: 1.0.0
**最后更新**: 2025-07-16
**维护者**: N100 Team
