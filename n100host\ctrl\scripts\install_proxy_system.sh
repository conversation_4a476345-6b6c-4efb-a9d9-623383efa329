#!/bin/bash

# N100串口代理系统安装脚本
# 安装基于串口代理的新架构，避免串口冲突

set -e

echo "=== N100串口代理系统安装脚本 ==="

# 检查权限
if [[ $EUID -ne 0 ]]; then
   echo "错误: 此脚本需要root权限运行"
   echo "请使用: sudo $0"
   exit 1
fi

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "项目根目录: $PROJECT_ROOT"

# 1. 停止旧服务
echo "1. 停止旧服务..."
systemctl stop n100-shutdown 2>/dev/null || true
systemctl stop n100-serial-manager 2>/dev/null || true
systemctl disable n100-shutdown 2>/dev/null || true
systemctl disable n100-serial-manager 2>/dev/null || true

# 2. 创建安装目录
echo "2. 创建安装目录..."
mkdir -p /opt/n100/ctrl
mkdir -p /var/log

# 3. 复制源代码
echo "3. 复制源代码..."
cp -r "$PROJECT_ROOT/src"/* /opt/n100/ctrl/
chmod +x /opt/n100/ctrl/*.py

# 4. 设置串口权限
echo "4. 设置串口权限..."
if [ -e /dev/ttyS4 ]; then
    chmod 666 /dev/ttyS4
    echo "串口权限已设置"
else
    echo "警告: /dev/ttyS4 不存在，请检查硬件连接"
fi

# 5. 安装串口代理服务
echo "5. 安装串口代理服务..."
cp "$PROJECT_ROOT/services/n100-serial-proxy.service" /etc/systemd/system/
systemctl daemon-reload
systemctl enable n100-serial-proxy

# 6. 创建代理版关机服务
echo "6. 创建代理版关机服务..."
cat > /etc/systemd/system/n100-shutdown-proxy.service << 'EOF'
[Unit]
Description=N100 Shutdown Daemon (Proxy Version)
Documentation=man:n100-shutdown-proxy(8)
After=n100-serial-proxy.service
Wants=n100-serial-proxy.service
Requires=n100-serial-proxy.service

[Service]
Type=simple
ExecStart=/usr/bin/python3 /opt/n100/ctrl/n100_shutdown_daemon_proxy.py --socket /tmp/n100_serial_proxy.sock --log /var/log/n100_shutdown_proxy.log
Restart=always
RestartSec=5
User=root
Group=root

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=n100-shutdown-proxy

# 环境变量
Environment=PYTHONPATH=/opt/n100/ctrl
Environment=PYTHONUNBUFFERED=1

# 安全配置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/tmp /var/log

# 资源限制
MemoryMax=30M
CPUQuota=5%

[Install]
WantedBy=multi-user.target
EOF

systemctl enable n100-shutdown-proxy

# 7. 安装Python依赖
echo "7. 安装Python依赖..."
if command -v pip3 &> /dev/null; then
    pip3 install pyserial
else
    echo "警告: pip3 未找到，请手动安装 pyserial"
fi

# 8. 创建CLI工具链接
echo "8. 创建CLI工具链接..."
cat > /usr/local/bin/n100-power-ctrl << 'EOF'
#!/bin/bash
# N100电源控制CLI工具

# 使用代理模式
exec /usr/bin/python3 /opt/n100/ctrl/power_ctrl_cli.py --use-proxy "$@"
EOF

chmod +x /usr/local/bin/n100-power-ctrl

# 9. 创建测试脚本
echo "9. 创建测试脚本..."
cat > /usr/local/bin/n100-test-proxy << 'EOF'
#!/bin/bash
# N100代理系统测试脚本

echo "=== N100代理系统测试 ==="

# 检查服务状态
echo "1. 检查服务状态:"
systemctl is-active n100-serial-proxy && echo "  ✅ 串口代理服务运行正常" || echo "  ❌ 串口代理服务异常"
systemctl is-active n100-shutdown-proxy && echo "  ✅ 关机守护进程运行正常" || echo "  ❌ 关机守护进程异常"

# 检查Socket
echo "2. 检查Socket:"
if [ -S /tmp/n100_serial_proxy.sock ]; then
    echo "  ✅ 代理Socket存在"
else
    echo "  ❌ 代理Socket不存在"
fi

# 检查串口
echo "3. 检查串口:"
if [ -e /dev/ttyS4 ]; then
    echo "  ✅ 串口设备存在"
    ls -l /dev/ttyS4
else
    echo "  ❌ 串口设备不存在"
fi

# 检查进程
echo "4. 检查进程:"
if pgrep -f "serial_proxy_daemon" > /dev/null; then
    echo "  ✅ 串口代理进程运行中"
else
    echo "  ❌ 串口代理进程未运行"
fi

if pgrep -f "n100_shutdown_daemon_proxy" > /dev/null; then
    echo "  ✅ 关机守护进程运行中"
else
    echo "  ❌ 关机守护进程未运行"
fi

# 测试LED控制
echo "5. 测试LED控制:"
if /usr/local/bin/n100-power-ctrl led normal; then
    echo "  ✅ LED控制测试成功"
else
    echo "  ❌ LED控制测试失败"
fi

echo "=== 测试完成 ==="
EOF

chmod +x /usr/local/bin/n100-test-proxy

# 10. 启动服务
echo "10. 启动服务..."
systemctl start n100-serial-proxy
sleep 2
systemctl start n100-shutdown-proxy

# 11. 检查服务状态
echo "11. 检查服务状态..."
echo "串口代理服务:"
systemctl status n100-serial-proxy --no-pager -l

echo "关机守护进程:"
systemctl status n100-shutdown-proxy --no-pager -l

# 12. 显示安装结果
echo ""
echo "=== 安装完成 ==="
echo "✅ 串口代理系统已安装"
echo ""
echo "服务状态:"
systemctl is-active n100-serial-proxy && echo "  ✅ n100-serial-proxy: 运行中" || echo "  ❌ n100-serial-proxy: 异常"
systemctl is-active n100-shutdown-proxy && echo "  ✅ n100-shutdown-proxy: 运行中" || echo "  ❌ n100-shutdown-proxy: 异常"

echo ""
echo "使用方法:"
echo "  LED控制: n100-power-ctrl led normal"
echo "  呼吸灯:  n100-power-ctrl breath 3"
echo "  测试:    n100-power-ctrl test"
echo "  系统测试: n100-test-proxy"

echo ""
echo "日志查看:"
echo "  代理服务: journalctl -u n100-serial-proxy -f"
echo "  关机服务: journalctl -u n100-shutdown-proxy -f"

echo ""
echo "🎉 安装成功！新架构避免了串口冲突问题。"
