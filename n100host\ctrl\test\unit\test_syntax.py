#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语法和基本功能测试脚本
用于验证程序的正确性（不需要实际的串口设备）
"""

import sys
import os
from unittest.mock import MagicMock, patch

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))


def test_import():
    """测试模块导入"""
    try:
        # 模拟serial模块
        mock_serial = MagicMock()
        mock_serial.Serial = MagicMock()
        mock_serial.EIGHTBITS = 8
        mock_serial.PARITY_NONE = 'N'
        mock_serial.STOPBITS_ONE = 1
        
        with patch.dict('sys.modules', {'serial': mock_serial}):
            from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod, PowerCommand
            print("✓ 模块导入成功")
            return True, (N100PowerController, LEDMode, BreathPeriod, PowerCommand)
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False, None


def test_enums():
    """测试枚举定义"""
    try:
        _, (N100PowerController, LEDMode, BreathPeriod, PowerCommand) = test_import()
        
        # 测试LEDMode枚举
        assert LEDMode.NORMAL == 0x00
        assert LEDMode.BREATH == 0x01
        print("✓ LEDMode枚举定义正确")
        
        # 测试BreathPeriod枚举
        assert BreathPeriod.PERIOD_1S == 1
        assert BreathPeriod.PERIOD_3S == 3
        assert BreathPeriod.PERIOD_5S == 5
        print("✓ BreathPeriod枚举定义正确")
        
        # 测试PowerCommand枚举
        assert PowerCommand.LED_MODE == 0x01
        assert PowerCommand.BREATH_PERIOD == 0x02
        assert PowerCommand.SHUTDOWN == 0x03
        assert PowerCommand.ACK == 0x80
        print("✓ PowerCommand枚举定义正确")
        
        return True
    except Exception as e:
        print(f"✗ 枚举测试失败: {e}")
        return False


def test_frame_creation():
    """测试消息帧创建"""
    try:
        # 模拟serial模块
        mock_serial = MagicMock()
        mock_serial.Serial = MagicMock()
        mock_serial.EIGHTBITS = 8
        mock_serial.PARITY_NONE = 'N'
        mock_serial.STOPBITS_ONE = 1
        
        with patch.dict('sys.modules', {'serial': mock_serial}):
            from n100_power_ctrl import N100PowerController, LEDMode, PowerCommand
            
            # 创建控制器实例
            controller = N100PowerController()
            
            # 测试各种帧的创建 (根据原始需求)
            test_cases = [
                (PowerCommand.LED_MODE, bytes([LEDMode.NORMAL]), "AA 02 01 00 FF 55"),
                (PowerCommand.LED_MODE, bytes([LEDMode.BREATH]), "AA 02 01 01 FE 55"),
                (PowerCommand.BREATH_PERIOD, bytes([1]), "AA 02 02 01 FD 55"),
                (PowerCommand.BREATH_PERIOD, bytes([3]), "AA 02 02 03 FB 55"),
                (PowerCommand.BREATH_PERIOD, bytes([5]), "AA 02 02 05 F9 55"),
                (PowerCommand.SHUTDOWN, b'', "AA 01 03 FD 55"),
            ]
            
            for command, data, expected in test_cases:
                frame = controller._create_frame(command, data)
                frame_hex = frame.hex(' ').upper()
                if frame_hex == expected:
                    print(f"✓ 帧创建正确: {expected}")
                else:
                    print(f"✗ 帧创建错误: 期望 {expected}, 实际 {frame_hex}")
                    return False
            
            return True
    except Exception as e:
        print(f"✗ 帧创建测试失败: {e}")
        return False


def test_checksum():
    """测试校验和计算"""
    try:
        mock_serial = MagicMock()
        mock_serial.Serial = MagicMock()
        mock_serial.EIGHTBITS = 8
        mock_serial.PARITY_NONE = 'N'
        mock_serial.STOPBITS_ONE = 1
        
        with patch.dict('sys.modules', {'serial': mock_serial}):
            from n100_power_ctrl import N100PowerController
            
            controller = N100PowerController()
            
            # 测试校验和计算 (只对命令+数据计算)
            test_cases = [
                (bytes([0x01, 0x00]), 0xFF),  # 命令1 + 数据0 = 1 -> (~1 + 1) = 0xFF
                (bytes([0x01, 0x01]), 0xFE),  # 命令1 + 数据1 = 2 -> (~2 + 1) = 0xFE
                (bytes([0x02, 0x01]), 0xFD),  # 命令2 + 数据1 = 3 -> (~3 + 1) = 0xFD
                (bytes([0x02, 0x03]), 0xFB),  # 命令2 + 数据3 = 5 -> (~5 + 1) = 0xFB
                (bytes([0x02, 0x05]), 0xF9),  # 命令2 + 数据5 = 7 -> (~7 + 1) = 0xF9
                (bytes([0x03]), 0xFD),        # 命令3 = 3 -> (~3 + 1) = 0xFD
            ]
            
            for data, expected in test_cases:
                checksum = controller._calculate_checksum(data)
                if checksum == expected:
                    print(f"✓ 校验和正确: {data.hex(' ').upper()} -> 0x{checksum:02X}")
                else:
                    print(f"✗ 校验和错误: {data.hex(' ').upper()} 期望 0x{expected:02X}, 实际 0x{checksum:02X}")
                    return False
            
            return True
    except Exception as e:
        print(f"✗ 校验和测试失败: {e}")
        return False


def test_ack_parsing():
    """测试ACK帧解析"""
    try:
        mock_serial = MagicMock()
        mock_serial.Serial = MagicMock()
        mock_serial.EIGHTBITS = 8
        mock_serial.PARITY_NONE = 'N'
        mock_serial.STOPBITS_ONE = 1
        
        with patch.dict('sys.modules', {'serial': mock_serial}):
            from n100_power_ctrl import N100PowerController
            
            controller = N100PowerController()
            
            # 测试ACK帧解析
            ack_frame = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
            controller._rx_buffer = bytearray(ack_frame)
            
            if controller._parse_ack_frame():
                print("✓ ACK帧解析成功")
                return True
            else:
                print("✗ ACK帧解析失败")
                return False
                
    except Exception as e:
        print(f"✗ ACK解析测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始语法和基本功能测试...")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_import),
        ("枚举定义", test_enums),
        ("消息帧创建", test_frame_creation),
        ("校验和计算", test_checksum),
        ("ACK帧解析", test_ack_parsing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序可以正常使用。")
        return 0
    else:
        print("❌ 部分测试失败，请检查代码。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
