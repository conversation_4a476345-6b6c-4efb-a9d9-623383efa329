#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100关机守护进程专项测试
测试关机守护进程是否能正确接收和处理关机请求
"""

import os
import sys
import time
import threading
import subprocess

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

try:
    from serial_proxy_client import SerialProxyClient
    from serial_proxy_daemon import ProxyMessage, MessageType
except ImportError as e:
    print(f"错误: 无法导入模块: {e}")
    sys.exit(1)


class ShutdownDaemonTester:
    """关机守护进程测试器"""
    
    def __init__(self):
        self.client = None
        self.received_messages = []
        self.shutdown_daemon_running = False
    
    def check_shutdown_daemon_status(self):
        """检查关机守护进程状态"""
        try:
            result = subprocess.run(
                ["systemctl", "is-active", "n100-shutdown-proxy"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0 and result.stdout.strip() == "active":
                self.shutdown_daemon_running = True
                print("✅ 关机守护进程正在运行")
                return True
            else:
                print("❌ 关机守护进程未运行")
                print("启动命令: sudo systemctl start n100-shutdown-proxy")
                return False
        
        except Exception as e:
            print(f"⚠️  无法检查关机守护进程状态: {e}")
            return False
    
    def setup_client(self):
        """设置测试客户端"""
        print("设置测试客户端...")
        
        self.client = SerialProxyClient("shutdown_daemon_tester")
        self.client.set_message_callback(self._on_message)
        self.client.set_error_callback(self._on_error)
        
        # 不设置过滤器，接收所有消息
        
        if not self.client.connect():
            print("❌ 无法连接到串口代理服务")
            return False
        
        print("✅ 测试客户端连接成功")
        return True
    
    def _on_message(self, message: ProxyMessage):
        """消息回调"""
        self.received_messages.append(message)
        print(f"收到消息: 类型={message.msg_type}, 命令=0x{message.command:02X}, 数据={message.data.hex(' ').upper()}")
    
    def _on_error(self, error_msg: str):
        """错误回调"""
        print(f"客户端错误: {error_msg}")
    
    def test_direct_serial_write(self):
        """测试直接向串口写入关机请求"""
        print("\n=== 测试直接串口写入 ===")
        
        try:
            # 直接向串口写入关机请求
            shutdown_request = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
            print(f"直接向串口写入关机请求: {shutdown_request.hex(' ').upper()}")
            
            with open('/dev/ttyS4', 'wb') as serial_port:
                serial_port.write(shutdown_request)
                serial_port.flush()
            
            print("✅ 关机请求已写入串口")
            
            # 等待处理
            print("等待关机守护进程处理...")
            time.sleep(3)
            
            # 检查是否收到关机成功消息
            shutdown_success_received = False
            for msg in self.received_messages:
                if msg.command == 0x03:  # SHUTDOWN_SUCCESS
                    shutdown_success_received = True
                    print("✅ 收到关机成功消息")
                    break
            
            if not shutdown_success_received:
                print("⚠️  未收到关机成功消息")
                print("可能原因：关机守护进程未运行或消息过滤问题")
            
            return True
        
        except PermissionError:
            print("❌ 权限不足，无法写入串口")
            print("请确保当前用户有串口写入权限")
            return False
        
        except Exception as e:
            print(f"❌ 直接串口写入失败: {e}")
            return False
    
    def test_proxy_forwarding(self):
        """测试代理转发功能"""
        print("\n=== 测试代理转发功能 ===")
        
        # 清空接收消息
        self.received_messages.clear()
        
        # 通过代理发送关机请求
        shutdown_request = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
        print(f"通过代理发送关机请求: {shutdown_request.hex(' ').upper()}")
        
        if self.client.send_data(shutdown_request):
            print("✅ 关机请求通过代理发送成功")
        else:
            print("❌ 关机请求通过代理发送失败")
            return False
        
        # 等待处理
        print("等待关机守护进程处理...")
        time.sleep(3)
        
        # 检查是否收到关机成功消息
        shutdown_success_received = False
        for msg in self.received_messages:
            if msg.command == 0x03:  # SHUTDOWN_SUCCESS
                shutdown_success_received = True
                print("✅ 收到关机成功消息")
                break
        
        if not shutdown_success_received:
            print("⚠️  未收到关机成功消息")
        
        return True
    
    def check_logs(self):
        """检查相关日志"""
        print("\n=== 检查日志 ===")
        
        print("串口代理日志 (最近10条):")
        try:
            result = subprocess.run(
                ["journalctl", "-u", "n100-serial-proxy", "-n", "10", "--no-pager"],
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if line.strip():
                        print(f"  {line}")
            else:
                print("  无法获取串口代理日志")
        except Exception as e:
            print(f"  获取串口代理日志失败: {e}")
        
        print("\n关机守护进程日志 (最近10条):")
        try:
            result = subprocess.run(
                ["journalctl", "-u", "n100-shutdown-proxy", "-n", "10", "--no-pager"],
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if line.strip():
                        print(f"  {line}")
            else:
                print("  无法获取关机守护进程日志")
        except Exception as e:
            print(f"  获取关机守护进程日志失败: {e}")
    
    def run_tests(self):
        """运行所有测试"""
        print("N100关机守护进程专项测试")
        print("=" * 50)
        
        # 检查关机守护进程状态
        daemon_running = self.check_shutdown_daemon_status()
        
        # 设置客户端
        if not self.setup_client():
            return 1
        
        try:
            # 测试1: 代理转发功能
            proxy_test = self.test_proxy_forwarding()
            
            # 测试2: 直接串口写入（如果有权限）
            direct_test = self.test_direct_serial_write()
            
            # 检查日志
            self.check_logs()
            
            # 生成报告
            print("\n" + "=" * 50)
            print("测试结果")
            print("=" * 50)
            
            print(f"关机守护进程状态: {'✅ 运行中' if daemon_running else '❌ 未运行'}")
            print(f"代理转发测试: {'✅ 通过' if proxy_test else '❌ 失败'}")
            print(f"直接串口测试: {'✅ 通过' if direct_test else '❌ 失败'}")
            
            if not daemon_running:
                print("\n⚠️  关机守护进程未运行，请先启动:")
                print("  sudo systemctl start n100-shutdown-proxy")
                print("  或手动启动: python3 start_shutdown_daemon.py")
            
            if daemon_running and (proxy_test or direct_test):
                print("\n🎉 关机守护进程测试基本通过！")
                print("注意：实际关机测试会导致系统关机，请谨慎操作。")
                return 0
            else:
                print("\n❌ 关机守护进程测试失败，请检查配置。")
                return 1
        
        finally:
            if self.client:
                self.client.disconnect()


def main():
    """主函数"""
    tester = ShutdownDaemonTester()
    
    try:
        return tester.run_tests()
    
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        return 1
    
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
