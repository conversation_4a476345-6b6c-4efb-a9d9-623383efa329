#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
关机流程测试脚本
模拟完整的关机流程测试
"""

import os
import sys
import time
import threading
import subprocess
import signal

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from n100_shutdown_daemon import N100ShutdownDaemon
    from power_board_simulator import PowerBoardSimulator
    from protocol import CommandType
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    sys.exit(1)


class ShutdownFlowTester:
    """关机流程测试器"""
    
    def __init__(self):
        self.n100_port = '/tmp/ttyS4_n100'
        self.power_port = '/tmp/ttyS4_power'
        self.simulator = None
        self.daemon = None
        self.daemon_thread = None
        self.test_results = {}
        
    def setup_virtual_serial(self) -> bool:
        """设置虚拟串口"""
        print("=== 设置虚拟串口 ===")
        
        try:
            # 创建虚拟串口对
            cmd = [
                'socat', 
                f'pty,raw,echo=0,link={self.n100_port}',
                f'pty,raw,echo=0,link={self.power_port}'
            ]
            
            self.socat_process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            time.sleep(2)
            
            if os.path.exists(self.n100_port) and os.path.exists(self.power_port):
                print(f"✅ 虚拟串口创建成功")
                return True
            else:
                print("❌ 虚拟串口创建失败")
                return False
                
        except Exception as e:
            print(f"❌ 设置虚拟串口失败: {e}")
            return False
    
    def start_power_board_simulator(self) -> bool:
        """启动电源板模拟器"""
        print("\n=== 启动电源板模拟器 ===")
        
        try:
            self.simulator = PowerBoardSimulator(port=self.power_port)
            
            if self.simulator.start():
                print("✅ 电源板模拟器启动成功")
                return True
            else:
                print("❌ 电源板模拟器启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动电源板模拟器失败: {e}")
            return False
    
    def start_shutdown_daemon(self) -> bool:
        """启动关机守护进程"""
        print("\n=== 启动关机守护进程 ===")
        
        try:
            # 创建关机守护进程（使用虚拟串口）
            self.daemon = N100ShutdownDaemon(port=self.n100_port)
            
            # 在单独线程中启动守护进程
            def daemon_worker():
                try:
                    self.daemon.start()
                except Exception as e:
                    print(f"关机守护进程异常: {e}")
            
            self.daemon_thread = threading.Thread(target=daemon_worker, daemon=True)
            self.daemon_thread.start()
            
            # 等待守护进程启动
            time.sleep(2)
            
            if self.daemon.running:
                print("✅ 关机守护进程启动成功")
                return True
            else:
                print("❌ 关机守护进程启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动关机守护进程失败: {e}")
            return False
    
    def test_shutdown_request_handling(self) -> bool:
        """测试关机请求处理"""
        print("\n=== 测试关机请求处理 ===")
        
        if not self.simulator or not self.daemon:
            print("❌ 模拟器或守护进程未启动")
            return False
        
        try:
            print("1. 发送关机请求...")
            self.simulator.send_shutdown_request()
            
            # 等待守护进程处理
            time.sleep(3)
            
            # 检查是否收到ACK应答
            if self.simulator.stats['sent_shutdown_requests'] > 0:
                print("✅ 关机请求已发送")
                
                # 检查守护进程是否处理了关机请求
                if self.daemon.shutdown_requested:
                    print("✅ 关机守护进程已处理关机请求")
                    return True
                else:
                    print("❌ 关机守护进程未处理关机请求")
                    return False
            else:
                print("❌ 关机请求发送失败")
                return False
                
        except Exception as e:
            print(f"❌ 关机请求处理测试失败: {e}")
            return False
    
    def test_shutdown_notify_script(self) -> bool:
        """测试关机通知脚本"""
        print("\n=== 测试关机通知脚本 ===")
        
        try:
            # 直接测试关机通知脚本
            from shutdown_notify import main as shutdown_notify_main
            
            print("运行关机通知脚本...")
            
            # 在单独线程中运行，避免阻塞
            def run_notify():
                try:
                    # 模拟关机通知脚本的执行
                    success = N100ShutdownDaemon.send_shutdown_success(self.n100_port)
                    self.test_results['shutdown_notify'] = success
                except Exception as e:
                    print(f"关机通知脚本异常: {e}")
                    self.test_results['shutdown_notify'] = False
            
            notify_thread = threading.Thread(target=run_notify, daemon=True)
            notify_thread.start()
            notify_thread.join(timeout=5)
            
            success = self.test_results.get('shutdown_notify', False)
            print(f"关机通知脚本结果: {'✅ 成功' if success else '❌ 失败'}")
            
            return success
            
        except Exception as e:
            print(f"❌ 关机通知脚本测试失败: {e}")
            return False
    
    def test_complete_shutdown_flow(self) -> bool:
        """测试完整关机流程"""
        print("\n=== 测试完整关机流程 ===")
        
        try:
            # 1. 发送关机请求
            print("步骤1: 发送关机请求")
            self.simulator.send_shutdown_request()
            time.sleep(1)
            
            # 2. 检查ACK应答
            print("步骤2: 检查ACK应答")
            # 这里应该检查模拟器是否收到了ACK
            
            # 3. 模拟关机通知
            print("步骤3: 模拟关机成功通知")
            success = N100ShutdownDaemon.send_shutdown_success(self.n100_port)
            
            if success:
                print("✅ 完整关机流程测试成功")
                return True
            else:
                print("❌ 关机成功通知发送失败")
                return False
                
        except Exception as e:
            print(f"❌ 完整关机流程测试失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        print("\n=== 清理资源 ===")
        
        # 停止关机守护进程
        if self.daemon:
            self.daemon.stop()
        
        # 停止电源板模拟器
        if self.simulator:
            self.simulator.stop()
        
        # 停止socat进程
        if hasattr(self, 'socat_process'):
            self.socat_process.terminate()
            self.socat_process.wait()
        
        # 删除虚拟串口文件
        for port in [self.n100_port, self.power_port]:
            if os.path.exists(port):
                try:
                    os.unlink(port)
                except:
                    pass
        
        print("✅ 资源清理完成")
    
    def run_full_test(self):
        """运行完整测试"""
        print("N100关机流程完整测试")
        print("=" * 40)
        
        results = {}
        
        try:
            # 1. 设置虚拟串口
            if not self.setup_virtual_serial():
                return False
            
            # 2. 启动电源板模拟器
            results['simulator'] = self.start_power_board_simulator()
            
            # 3. 启动关机守护进程
            results['daemon'] = self.start_shutdown_daemon()
            
            # 4. 测试关机请求处理
            results['shutdown_request'] = self.test_shutdown_request_handling()
            
            # 5. 测试关机通知脚本
            results['shutdown_notify'] = self.test_shutdown_notify_script()
            
            # 6. 测试完整关机流程
            results['complete_flow'] = self.test_complete_shutdown_flow()
            
            # 7. 显示测试结果
            print(f"\n=== 测试结果总结 ===")
            for test_name, result in results.items():
                status = "✅ 通过" if result else "❌ 失败"
                print(f"{test_name}: {status}")
            
            all_passed = all(results.values())
            if all_passed:
                print("\n🎉 所有关机流程测试通过！")
            else:
                print("\n❌ 部分测试失败，需要检查问题")
            
            return all_passed
            
        except KeyboardInterrupt:
            print("\n用户中断测试")
            return False
        except Exception as e:
            print(f"\n❌ 测试异常: {e}")
            return False
        finally:
            self.cleanup()


def main():
    """主函数"""
    tester = ShutdownFlowTester()
    
    try:
        success = tester.run_full_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被中断")
        tester.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    main()
