#ifndef __PROTOCOL_H_
#define __PROTOCOL_H_

#include "config.h"

//========================================================================
//                              协议定义
//========================================================================

// 协议帧头和帧尾
#define PROTOCOL_HEADER     0xAA        // 帧头
#define PROTOCOL_TAIL       0x55        // 帧尾

// 命令类型定义
// 接收的消息（自动发送CMD_ACK应答）
#define CMD_LED_MODE        0x01        // LED模式设置命令
#define CMD_BREATH_PERIOD   0x02        // 呼吸灯周期设置命令
#define CMD_SHUTDOWN        0x03        // 关机成功消息

// 发送的消息（等待CMD_ACK应答）
#define CMD_SHUTDOWN_REQ    0x13        // 关机请求命令

// 应答消息
#define CMD_ACK             0x80        // 通用应答命令（所有消息的应答都是这个）

// LED模式定义
#define LED_MODE_NORMAL     0x00        // LED正常模式
#define LED_MODE_BREATH     0x01        // LED呼吸灯模式

// 协议数据长度定义
#define PROTOCOL_MIN_LEN    5           // 最小协议长度(头+长度+命令+校验+尾)
#define PROTOCOL_MAX_LEN    20          // 最大协议长度
#define PROTOCOL_DATA_MAX   13          // 最大数据长度

// 协议帧结构
typedef struct
{
    u8 header;          // 帧头 0xAA
    u8 length;          // 数据长度(不包括帧头、长度、校验、帧尾)
    u8 command;         // 命令类型
    u8 databuff[PROTOCOL_DATA_MAX]; // 数据内容
    u8 checksum;        // 校验和
    u8 tail;            // 帧尾 0x55
} ProtocolFrame_t;

// 协议处理状态
typedef enum
{
    PROTOCOL_STATE_IDLE = 0,    // 空闲状态
    PROTOCOL_STATE_HEADER,      // 等待帧头
    PROTOCOL_STATE_LENGTH,      // 等待长度
    PROTOCOL_STATE_DATA,        // 接收数据
    PROTOCOL_STATE_CHECKSUM,    // 等待校验
    PROTOCOL_STATE_TAIL,        // 等待帧尾
    PROTOCOL_STATE_COMPLETE     // 接收完成
} ProtocolState_t;

// 协议接收缓冲区
typedef struct
{
    u8 buffer[PROTOCOL_MAX_LEN];    // 接收缓冲区
    u8 index;                       // 当前接收位置
    u8 data_length;                 // 数据长度
    u8 received_length;             // 已接收数据长度
    ProtocolState_t state;          // 接收状态
} ProtocolRxBuffer_t;

//========================================================================
//                              全局数据结构
//========================================================================

// 发送数据结构
typedef struct
{
    u8 shutdown_request;    // 要发送的关机请求标志
} ProtocolTxData_t;

// 接收数据结构
typedef struct
{
    u8 led_mode;            // 接收到的LED模式
    u8 breath_period;      // 接收到的呼吸灯周期(秒)
    u8 shutdown_success;    // 接收到的关机成功标志
    u8 data_updated;        // 数据更新标志 (0=无更新, 1=有新数据)
} ProtocolRxData_t;

// 分别为UART2和UART3设置发送和接收数据
extern ProtocolTxData_t g_uart2_tx_data;  // UART2发送数据
extern ProtocolRxData_t g_uart2_rx_data;  // UART2接收数据
extern ProtocolTxData_t g_uart3_tx_data;  // UART3发送数据
extern ProtocolRxData_t g_uart3_rx_data;  // UART3接收数据

//========================================================================
//                              函数声明
//========================================================================

// 协议初始化
void Protocol_Init(void);

// 发送协议函数 (uart_num: 2=UART2, 3=UART3)
void Protocol_SendLedMode(u8 uart_num, u8 led_mode);
void Protocol_SendBreathPeriod(u8 uart_num, u8 period_seconds);
void Protocol_SendShutdownRequest(u8 uart_num);
void Protocol_SendAck(u8 uart_num);  // 通用应答函数
void Protocol_SendShutdownAck(u8 uart_num);

// 接收协议处理函数 (uart_num: 2=UART2, 3=UART3)
u8 Protocol_ProcessRxData(u8 uart_num, u8 databuff);
void Protocol_HandleCommand(u8 uart_num, ProtocolFrame_t *frame);
void Protocol_ProcessRxData_ISR(u8 uart_num, u8 databuff);  // 中断中调用的处理函数

// 工具函数
u8 Protocol_CalculateChecksum(u8 *databuff, u8 length);
void Protocol_SendFrame(u8 uart_num, ProtocolFrame_t *frame);

// 数据访问函数
u8 Protocol_CheckDataUpdated(u8 uart_num);  // 检查并清除指定UART的数据更新标志

// 协议回调函数(用户需要实现)
extern void Protocol_OnLedModeReceived(u8 uart_num, u8 led_mode);
extern void Protocol_OnBreathPeriodReceived(u8 uart_num, u16 period_ms);
extern void Protocol_OnShutdownRequestReceived(u8 uart_num);
extern void Protocol_OnLedModeAckReceived(u8 uart_num, u8 led_mode);
extern void Protocol_OnBreathPeriodAckReceived(u8 uart_num, u16 period_ms);
extern void Protocol_OnShutdownAckReceived(u8 uart_num);

#endif
