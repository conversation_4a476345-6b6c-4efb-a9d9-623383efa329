#!/bin/bash

# N100直接架构安装脚本
# 安装直接串口版本的关机守护进程

set -e

echo "=== N100直接架构安装脚本 ==="

# 检查权限
if [[ $EUID -ne 0 ]]; then
   echo "错误: 此脚本需要root权限运行"
   echo "请使用: sudo $0"
   exit 1
fi

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "项目根目录: $PROJECT_ROOT"

# 1. 停止现有服务
echo "1. 停止现有服务..."
systemctl stop n100-shutdown 2>/dev/null || true
systemctl stop n100-serial-manager 2>/dev/null || true
systemctl stop n100-serial-proxy 2>/dev/null || true
systemctl stop n100-shutdown-proxy 2>/dev/null || true
systemctl stop n100-shutdown-direct 2>/dev/null || true

systemctl disable n100-shutdown 2>/dev/null || true
systemctl disable n100-serial-manager 2>/dev/null || true
systemctl disable n100-serial-proxy 2>/dev/null || true
systemctl disable n100-shutdown-proxy 2>/dev/null || true
systemctl disable n100-shutdown-direct 2>/dev/null || true

# 2. 创建安装目录
echo "2. 创建安装目录..."
mkdir -p /opt/n100/ctrl
mkdir -p /var/log

# 3. 复制源代码
echo "3. 复制源代码..."
cp -r "$PROJECT_ROOT/src"/* /opt/n100/ctrl/
chmod +x /opt/n100/ctrl/*.py

# 4. 设置串口权限
echo "4. 设置串口权限..."
if [ -e /dev/ttyS4 ]; then
    chmod 666 /dev/ttyS4
    echo "串口权限已设置"
else
    echo "警告: /dev/ttyS4 不存在，请检查硬件连接"
fi

# 5. 安装直接关机服务
echo "5. 安装直接关机服务..."
cp "$PROJECT_ROOT/services/n100-shutdown-direct.service" /etc/systemd/system/
systemctl daemon-reload
systemctl enable n100-shutdown-direct

# 6. 安装Python依赖
echo "6. 安装Python依赖..."
if command -v pip3 &> /dev/null; then
    pip3 install pyserial
else
    echo "警告: pip3 未找到，请手动安装 pyserial"
fi

# 7. 创建测试脚本
echo "7. 创建测试脚本..."
cat > /usr/local/bin/n100-test-direct << 'EOF'
#!/bin/bash
# N100直接架构测试脚本

echo "=== N100直接架构测试 ==="

# 检查服务状态
echo "1. 检查服务状态:"
systemctl is-active n100-shutdown-direct && echo "  ✅ 关机服务运行正常" || echo "  ❌ 关机服务异常"

# 检查串口
echo "2. 检查串口:"
if [ -e /dev/ttyS4 ]; then
    echo "  ✅ 串口设备存在"
    ls -l /dev/ttyS4
else
    echo "  ❌ 串口设备不存在"
fi

# 检查进程
echo "3. 检查进程:"
if pgrep -f "n100_shutdown_daemon_direct" > /dev/null; then
    echo "  ✅ 关机守护进程运行中"
else
    echo "  ❌ 关机守护进程未运行"
fi

# 测试电源控制
echo "4. 测试电源控制:"
cd /opt/n100/ctrl
python3 power_ctrl_cli.py led normal && echo "  ✅ LED控制测试成功" || echo "  ❌ LED控制测试失败"

# 测试关机请求
echo "5. 发送测试关机请求:"
echo -ne '\xAA\x01\x13\xED\x55' | sudo tee /dev/ttyS4 > /dev/null
echo "  ✅ 关机请求已发送"

# 查看日志
echo "6. 最新日志:"
journalctl -u n100-shutdown-direct -n 10 --no-pager

echo "=== 测试完成 ==="
EOF

chmod +x /usr/local/bin/n100-test-direct

# 8. 启动服务
echo "8. 启动服务..."
systemctl start n100-shutdown-direct

# 9. 检查服务状态
echo "9. 检查服务状态..."
echo "关机守护进程:"
systemctl status n100-shutdown-direct --no-pager -l

# 10. 显示安装结果
echo ""
echo "=== 安装完成 ==="
echo "✅ 直接架构已安装"
echo ""
echo "服务状态:"
systemctl is-active n100-shutdown-direct && echo "  ✅ n100-shutdown-direct: 运行中" || echo "  ❌ n100-shutdown-direct: 异常"

echo ""
echo "使用方法:"
echo "  测试系统: n100-test-direct"
echo "  电源控制: cd /opt/n100/ctrl && python3 power_ctrl_cli.py led normal"
echo "  模拟关机请求: echo -ne '\\xAA\\x01\\x13\\xED\\x55' | sudo tee /dev/ttyS4"

echo ""
echo "日志查看:"
echo "  关机服务: journalctl -u n100-shutdown-direct -f"

echo ""
echo "🎉 安装成功！直接架构已启动。"
