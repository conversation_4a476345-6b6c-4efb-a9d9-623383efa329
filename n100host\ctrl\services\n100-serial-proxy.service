[Unit]
Description=N100 Serial Proxy Daemon
Documentation=man:n100-serial-proxy(8)
After=multi-user.target
Wants=multi-user.target

[Service]
Type=simple
ExecStart=/usr/bin/python3 /opt/n100/ctrl/serial_proxy_daemon.py --port /dev/ttyS4 --socket /tmp/n100_serial_proxy.sock --log /var/log/n100_serial_proxy.log
Restart=always
RestartSec=5
User=root
Group=root

# 串口访问权限
SupplementaryGroups=dialout

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=n100-serial-proxy

# 环境变量
Environment=PYTHONPATH=/opt/n100/ctrl
Environment=PYTHONUNBUFFERED=1

# 安全配置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/tmp /var/log /dev/ttyS4

# 资源限制
MemoryMax=50M
CPUQuota=10%

[Install]
WantedBy=multi-user.target
