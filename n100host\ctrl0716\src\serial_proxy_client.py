#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100串口代理客户端
通过Unix Socket连接到串口代理服务，实现串口通信
"""

import os
import sys
import json
import time
import socket
import threading
from typing import Optional, Callable, List
from dataclasses import dataclass

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from serial_proxy_daemon import ProxyMessage, MessageType
except ImportError as e:
    print(f"错误: 无法导入代理模块: {e}")
    sys.exit(1)


class SerialProxyClient:
    """串口代理客户端"""
    
    def __init__(self, 
                 client_id: str,
                 socket_path: str = '/tmp/n100_serial_proxy.sock',
                 timeout: float = 5.0):
        """
        初始化串口代理客户端
        
        参数:
            client_id: 客户端唯一标识
            socket_path: Unix Socket路径
            timeout: 连接超时时间
        """
        self.client_id = client_id
        self.socket_path = socket_path
        self.timeout = timeout
        
        # Socket连接
        self.socket = None
        self.connected = False
        
        # 消息处理
        self.message_callback: Optional[Callable] = None
        self.error_callback: Optional[Callable] = None
        
        # 接收线程
        self.receive_thread = None
        self.running = False
        
        # 消息过滤器
        self.message_filters: List[int] = []
    
    def connect(self) -> bool:
        """连接到串口代理服务"""
        try:
            # 创建Unix Socket
            self.socket = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            
            # 连接到代理服务
            self.socket.connect(self.socket_path)
            
            # 注册客户端
            register_msg = ProxyMessage('register', self.client_id)
            self._send_message(register_msg)
            
            # 启动接收线程
            self.running = True
            self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
            self.receive_thread.start()
            
            self.connected = True
            return True
            
        except Exception as e:
            print(f"连接代理服务失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        self.running = False
        self.connected = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
    
    def send_data(self, data: bytes) -> bool:
        """发送数据到串口"""
        if not self.connected:
            return False
        
        try:
            send_msg = ProxyMessage(
                msg_type='send_request',
                client_id=self.client_id,
                data=data
            )
            self._send_message(send_msg)
            return True
            
        except Exception as e:
            if self.error_callback:
                self.error_callback(f"发送数据失败: {e}")
            return False
    
    def set_message_callback(self, callback: Callable):
        """设置消息回调函数"""
        self.message_callback = callback
    
    def set_error_callback(self, callback: Callable):
        """设置错误回调函数"""
        self.error_callback = callback
    
    def set_message_filter(self, commands: List[int]):
        """设置消息过滤器"""
        self.message_filters = commands
        
        if self.connected:
            try:
                filter_msg = ProxyMessage(
                    msg_type='set_filter',
                    client_id=self.client_id,
                    data=commands
                )
                self._send_message(filter_msg)
            except Exception as e:
                if self.error_callback:
                    self.error_callback(f"设置过滤器失败: {e}")
    
    def _send_message(self, message: ProxyMessage):
        """发送消息到代理服务"""
        if not self.socket:
            raise Exception("Socket未连接")
        
        data = message.to_json().encode('utf-8') + b'\n'
        self.socket.send(data)
    
    def _receive_loop(self):
        """接收消息循环"""
        buffer = b''
        
        while self.running:
            try:
                data = self.socket.recv(4096)
                if not data:
                    break
                
                buffer += data
                
                # 处理完整的消息（以换行符分隔）
                while b'\n' in buffer:
                    line, buffer = buffer.split(b'\n', 1)
                    if line:
                        try:
                            message = ProxyMessage.from_json(line.decode('utf-8'))
                            self._handle_message(message)
                        except Exception as e:
                            if self.error_callback:
                                self.error_callback(f"解析消息失败: {e}")
            
            except Exception as e:
                if self.running:
                    if self.error_callback:
                        self.error_callback(f"接收消息异常: {e}")
                break
        
        self.connected = False
    
    def _handle_message(self, message: ProxyMessage):
        """处理接收到的消息"""
        if message.msg_type == 'register_ack':
            # 注册确认
            print(f"客户端 {self.client_id} 注册成功")
            
            # 设置消息过滤器
            if self.message_filters:
                self.set_message_filter(self.message_filters)
        
        elif message.msg_type == 'serial_data':
            # 串口数据
            if self.message_callback:
                self.message_callback(message)
        
        else:
            # 其他消息类型
            if self.message_callback:
                self.message_callback(message)


class SerialProxyInterface:
    """串口代理接口 - 兼容原有串口接口"""
    
    def __init__(self, client_id: str, socket_path: str = '/tmp/n100_serial_proxy.sock'):
        """
        初始化串口代理接口
        
        参数:
            client_id: 客户端ID
            socket_path: Socket路径
        """
        self.client = SerialProxyClient(client_id, socket_path)
        self.is_open = False
        
        # 接收缓冲区
        self.rx_buffer = bytearray()
        self.rx_lock = threading.Lock()
        
        # 设置消息回调
        self.client.set_message_callback(self._on_message)
        self.client.set_error_callback(self._on_error)
    
    def open(self) -> bool:
        """打开连接"""
        if self.client.connect():
            self.is_open = True
            return True
        return False
    
    def close(self):
        """关闭连接"""
        self.client.disconnect()
        self.is_open = False
    
    def write(self, data: bytes) -> int:
        """写入数据"""
        if not self.is_open:
            raise Exception("连接未打开")
        
        if self.client.send_data(data):
            return len(data)
        else:
            raise Exception("发送数据失败")
    
    def flush(self):
        """刷新缓冲区（代理模式下无需实现）"""
        pass
    
    def read(self, size: int = 1) -> bytes:
        """读取数据"""
        with self.rx_lock:
            if len(self.rx_buffer) >= size:
                data = bytes(self.rx_buffer[:size])
                self.rx_buffer = self.rx_buffer[size:]
                return data
            return b''
    
    @property
    def in_waiting(self) -> int:
        """获取等待读取的字节数"""
        with self.rx_lock:
            return len(self.rx_buffer)
    
    def reset_input_buffer(self):
        """清空输入缓冲区"""
        with self.rx_lock:
            self.rx_buffer.clear()
    
    def reset_output_buffer(self):
        """清空输出缓冲区（代理模式下无需实现）"""
        pass
    
    def _on_message(self, message: ProxyMessage):
        """处理接收到的消息"""
        if message.msg_type == 'serial_data':
            with self.rx_lock:
                self.rx_buffer.extend(message.data)
    
    def _on_error(self, error: str):
        """处理错误"""
        print(f"串口代理错误: {error}")


def create_proxy_serial(client_id: str, socket_path: str = '/tmp/n100_serial_proxy.sock'):
    """
    创建串口代理接口
    
    参数:
        client_id: 客户端ID
        socket_path: Socket路径
    
    返回:
        SerialProxyInterface: 串口代理接口
    """
    return SerialProxyInterface(client_id, socket_path)


# 测试代码
if __name__ == "__main__":
    import time
    
    def test_proxy_client():
        """测试代理客户端"""
        print("测试串口代理客户端...")
        
        # 创建客户端
        client = SerialProxyClient("test_client")
        
        # 设置回调
        def on_message(message):
            print(f"收到消息: {message.msg_type}, 命令: 0x{message.command:02X}, 数据: {message.data.hex(' ').upper()}")
        
        def on_error(error):
            print(f"错误: {error}")
        
        client.set_message_callback(on_message)
        client.set_error_callback(on_error)
        
        # 连接
        if client.connect():
            print("连接成功")
            
            # 发送测试数据
            test_data = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
            print(f"发送测试数据: {test_data.hex(' ').upper()}")
            client.send_data(test_data)
            
            # 等待响应
            time.sleep(2)
            
            client.disconnect()
        else:
            print("连接失败")
    
    test_proxy_client()
