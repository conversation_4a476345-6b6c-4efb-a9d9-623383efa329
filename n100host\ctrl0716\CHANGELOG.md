# 更新日志

本文档记录了N100电源控制系统的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
版本号遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划添加
- Web管理界面
- 配置文件支持
- 更多硬件平台支持
- 性能监控仪表板

## [1.0.0] - 2025-07-16

### 新增
- 完整的N100电源控制系统
- 支持两种架构：代理串口服务架构和直接关机服务架构
- 核心电源控制器 (`N100PowerController`)
- 串口通信协议实现
- 命令行工具 (`power_ctrl_cli.py`)
- 系统服务配置文件
- 自动化部署脚本
- 完整的测试套件
- 详细的文档

### 功能特性
- LED模式控制（正常/呼吸）
- 呼吸灯周期设置（1秒/3秒/5秒）
- 关机成功通知
- 自定义命令支持
- 自动重试机制
- ACK应答验证
- 回调函数支持
- 错误处理和恢复

### 架构支持
- **代理串口服务架构**：
  - 串口代理守护进程 (`serial_proxy_daemon.py`)
  - 串口代理客户端 (`serial_proxy_client.py`)
  - 关机守护进程代理版 (`n100_shutdown_daemon_proxy.py`)
  - Unix Socket通信
  - 消息路由和转发
  - 避免串口冲突

- **直接关机服务架构**：
  - 直接关机守护进程 (`n100_shutdown_daemon_direct.py`)
  - 直接串口访问
  - 简单部署

### 部署和管理
- 自动化安装脚本
  - `install_proxy_system.sh` - 代理架构安装
  - `install_direct_system.sh` - 直接架构安装
  - `uninstall.sh` - 系统卸载
  - `test_system.sh` - 系统测试
- 系统服务配置
  - `n100-serial-proxy.service`
  - `n100-shutdown-proxy.service`
  - `n100-shutdown-direct.service`
- 测试和监控工具
  - `n100-test-proxy`
  - `n100-test-direct`

### 文档
- 主要说明文档 (`README.md`)
- 通信协议文档 (`docs/PROTOCOL.md`)
- API参考文档 (`docs/API.md`)
- 部署指南 (`docs/DEPLOYMENT.md`)
- 故障排除指南 (`docs/TROUBLESHOOTING.md`)

### 示例和测试
- 基础使用示例 (`examples/basic_example.py`)
- 高级使用示例 (`examples/advanced_example.py`)
- 协议测试 (`tests/test_protocol.py`)
- 电源控制器测试 (`tests/test_power_ctrl.py`)
- 集成测试 (`tests/test_integration.py`)
- 测试运行器 (`tests/run_all_tests.py`)

### 通信协议
- 自定义串口通信协议
- 帧结构：帧头(0xAA) + 长度 + 命令 + 数据 + 校验和 + 帧尾(0x55)
- 支持的命令：
  - LED模式设置 (0x01)
  - 呼吸周期设置 (0x02)
  - 关机成功 (0x03)
  - 关机请求 (0x13)
  - ACK应答 (0x80)
- 校验和算法：二进制补码方法
- 自动重试和超时处理

### 技术规格
- Python 3.6+ 支持
- 串口通信：115200 bps, 8N1
- 默认串口：/dev/ttyS4
- 最大重试次数：10次
- 默认超时：1秒
- 支持的操作系统：Linux (Ubuntu 18.04+)

### 安全特性
- 服务权限控制
- 串口访问权限管理
- 系统资源限制
- 错误处理和恢复机制

### 性能优化
- 高效的串口通信
- 最小化资源占用
- 快速响应时间
- 并发连接支持（代理模式）

## 版本说明

### 版本号格式
版本号格式为 `主版本号.次版本号.修订号`：

- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 发布类型

- **[新增]** - 新功能
- **[变更]** - 对现有功能的变更
- **[弃用]** - 即将移除的功能
- **[移除]** - 已移除的功能
- **[修复]** - 问题修复
- **[安全]** - 安全相关的修复

## 升级指南

### 从开发版本升级到 1.0.0

这是首个正式版本，包含完整的功能实现。

#### 升级步骤

1. **备份现有配置**
   ```bash
   sudo cp -r /opt/n100 /backup/n100-backup-$(date +%Y%m%d)
   ```

2. **停止现有服务**
   ```bash
   sudo systemctl stop n100-*
   ```

3. **安装新版本**
   ```bash
   # 选择合适的架构
   sudo ./scripts/install_proxy_system.sh
   # 或
   sudo ./scripts/install_direct_system.sh
   ```

4. **验证升级**
   ```bash
   ./scripts/test_system.sh
   ```

#### 注意事项

- 首次安装无需特殊升级步骤
- 建议在生产环境使用代理架构
- 升级前请阅读部署指南

## 已知问题

### 1.0.0 版本

- 在某些硬件平台上可能需要调整串口设备路径
- Windows平台支持有限，主要针对Linux平台优化
- 高频率命令发送时可能出现偶发性超时

### 解决方案

- 串口设备路径问题：修改配置文件中的串口路径
- Windows平台：建议使用WSL或虚拟机运行
- 超时问题：增加命令间延迟或调整超时参数

## 贡献指南

欢迎贡献代码和报告问题！

### 报告问题

1. 检查已知问题列表
2. 在GitHub Issues中搜索类似问题
3. 创建新的Issue，包含：
   - 详细的问题描述
   - 重现步骤
   - 系统环境信息
   - 相关日志

### 提交代码

1. Fork项目仓库
2. 创建功能分支
3. 编写测试用例
4. 确保所有测试通过
5. 提交Pull Request

### 开发环境

```bash
# 克隆仓库
git clone https://github.com/n100team/ctrl.git
cd ctrl

# 安装开发依赖
pip install -r requirements.txt

# 运行测试
python tests/run_all_tests.py
```

## 支持

- **文档**: [项目Wiki](https://github.com/n100team/ctrl/wiki)
- **问题报告**: [GitHub Issues](https://github.com/n100team/ctrl/issues)
- **邮箱支持**: <EMAIL>

---

**维护者**: N100 Team  
**项目地址**: https://github.com/n100team/ctrl  
**许可证**: MIT License
