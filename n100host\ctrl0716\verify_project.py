#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制系统工程验证脚本
验证工程文件的完整性和正确性
"""

import os
import sys
import subprocess
from pathlib import Path


class ProjectVerifier:
    """工程验证器"""
    
    def __init__(self, project_root=None):
        self.project_root = Path(project_root or os.path.dirname(os.path.abspath(__file__)))
        self.errors = []
        self.warnings = []
        self.success_count = 0
        self.total_checks = 0
    
    def check_file_exists(self, file_path, description=""):
        """检查文件是否存在"""
        self.total_checks += 1
        full_path = self.project_root / file_path
        
        if full_path.exists():
            print(f"✅ {description or file_path}")
            self.success_count += 1
            return True
        else:
            error_msg = f"❌ 缺失文件: {file_path} ({description})"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def check_directory_exists(self, dir_path, description=""):
        """检查目录是否存在"""
        self.total_checks += 1
        full_path = self.project_root / dir_path
        
        if full_path.is_dir():
            print(f"✅ {description or dir_path}")
            self.success_count += 1
            return True
        else:
            error_msg = f"❌ 缺失目录: {dir_path} ({description})"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def check_python_syntax(self, file_path):
        """检查Python文件语法"""
        self.total_checks += 1
        full_path = self.project_root / file_path
        
        if not full_path.exists():
            return False
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                compile(f.read(), str(full_path), 'exec')
            print(f"✅ Python语法检查: {file_path}")
            self.success_count += 1
            return True
        except SyntaxError as e:
            error_msg = f"❌ Python语法错误: {file_path} - {e}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
        except Exception as e:
            warning_msg = f"⚠️  Python文件检查警告: {file_path} - {e}"
            print(warning_msg)
            self.warnings.append(warning_msg)
            return True
    
    def check_shell_syntax(self, file_path):
        """检查Shell脚本语法"""
        self.total_checks += 1
        full_path = self.project_root / file_path
        
        if not full_path.exists():
            return False
        
        try:
            result = subprocess.run(
                ['bash', '-n', str(full_path)],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                print(f"✅ Shell语法检查: {file_path}")
                self.success_count += 1
                return True
            else:
                error_msg = f"❌ Shell语法错误: {file_path} - {result.stderr}"
                print(error_msg)
                self.errors.append(error_msg)
                return False
        except subprocess.TimeoutExpired:
            warning_msg = f"⚠️  Shell语法检查超时: {file_path}"
            print(warning_msg)
            self.warnings.append(warning_msg)
            return True
        except FileNotFoundError:
            warning_msg = f"⚠️  bash命令未找到，跳过Shell语法检查: {file_path}"
            print(warning_msg)
            self.warnings.append(warning_msg)
            return True
        except Exception as e:
            warning_msg = f"⚠️  Shell语法检查异常: {file_path} - {e}"
            print(warning_msg)
            self.warnings.append(warning_msg)
            return True
    
    def verify_project_structure(self):
        """验证项目结构"""
        print("=== 验证项目结构 ===")
        
        # 检查根目录文件
        root_files = [
            ("README.md", "主要说明文档"),
            ("CHANGELOG.md", "更新日志"),
            ("LICENSE", "许可证文件"),
            ("Makefile", "构建脚本"),
            ("setup.py", "Python包配置"),
            ("requirements.txt", "依赖包列表"),
            ("config.example.yml", "配置文件示例"),
            (".gitignore", "Git忽略文件"),
            ("PROJECT_SUMMARY.md", "项目总结"),
        ]
        
        for file_path, description in root_files:
            self.check_file_exists(file_path, description)
        
        # 检查目录结构
        directories = [
            ("src", "源代码目录"),
            ("services", "系统服务配置"),
            ("scripts", "部署和管理脚本"),
            ("examples", "使用示例"),
            ("tests", "测试文件"),
            ("docs", "详细文档"),
        ]
        
        for dir_path, description in directories:
            self.check_directory_exists(dir_path, description)
    
    def verify_source_code(self):
        """验证源代码"""
        print("\n=== 验证源代码 ===")
        
        source_files = [
            ("src/__init__.py", "模块初始化"),
            ("src/protocol.py", "通信协议定义"),
            ("src/n100_power_ctrl.py", "核心电源控制器"),
            ("src/power_ctrl_cli.py", "命令行工具"),
            ("src/serial_proxy_daemon.py", "串口代理守护进程"),
            ("src/serial_proxy_client.py", "串口代理客户端"),
            ("src/n100_shutdown_daemon_proxy.py", "关机守护进程(代理版)"),
            ("src/n100_shutdown_daemon_direct.py", "关机守护进程(直接版)"),
        ]
        
        for file_path, description in source_files:
            if self.check_file_exists(file_path, description):
                self.check_python_syntax(file_path)
    
    def verify_services(self):
        """验证服务配置"""
        print("\n=== 验证服务配置 ===")
        
        service_files = [
            ("services/n100-serial-proxy.service", "串口代理服务"),
            ("services/n100-shutdown-direct.service", "直接关机服务"),
        ]
        
        for file_path, description in service_files:
            self.check_file_exists(file_path, description)
    
    def verify_scripts(self):
        """验证脚本文件"""
        print("\n=== 验证脚本文件 ===")
        
        script_files = [
            ("scripts/install_proxy_system.sh", "代理架构安装脚本"),
            ("scripts/install_direct_system.sh", "直接架构安装脚本"),
            ("scripts/uninstall.sh", "卸载脚本"),
            ("scripts/test_system.sh", "系统测试脚本"),
        ]
        
        for file_path, description in script_files:
            if self.check_file_exists(file_path, description):
                self.check_shell_syntax(file_path)
    
    def verify_examples(self):
        """验证示例文件"""
        print("\n=== 验证示例文件 ===")
        
        example_files = [
            ("examples/basic_example.py", "基础使用示例"),
            ("examples/advanced_example.py", "高级使用示例"),
        ]
        
        for file_path, description in example_files:
            if self.check_file_exists(file_path, description):
                self.check_python_syntax(file_path)
    
    def verify_tests(self):
        """验证测试文件"""
        print("\n=== 验证测试文件 ===")
        
        test_files = [
            ("tests/test_protocol.py", "协议测试"),
            ("tests/test_power_ctrl.py", "电源控制器测试"),
            ("tests/test_integration.py", "集成测试"),
            ("tests/run_all_tests.py", "测试运行器"),
        ]
        
        for file_path, description in test_files:
            if self.check_file_exists(file_path, description):
                self.check_python_syntax(file_path)
    
    def verify_documentation(self):
        """验证文档文件"""
        print("\n=== 验证文档文件 ===")
        
        doc_files = [
            ("docs/PROTOCOL.md", "通信协议文档"),
            ("docs/API.md", "API参考文档"),
            ("docs/DEPLOYMENT.md", "部署指南"),
            ("docs/TROUBLESHOOTING.md", "故障排除指南"),
        ]
        
        for file_path, description in doc_files:
            self.check_file_exists(file_path, description)
    
    def verify_dependencies(self):
        """验证依赖关系"""
        print("\n=== 验证依赖关系 ===")
        
        # 检查Python版本
        self.total_checks += 1
        if sys.version_info >= (3, 6):
            print(f"✅ Python版本: {sys.version}")
            self.success_count += 1
        else:
            error_msg = f"❌ Python版本过低: {sys.version} (需要3.6+)"
            print(error_msg)
            self.errors.append(error_msg)
        
        # 检查关键依赖
        try:
            import serial
            print("✅ pyserial模块可用")
            self.success_count += 1
        except ImportError:
            warning_msg = "⚠️  pyserial模块未安装"
            print(warning_msg)
            self.warnings.append(warning_msg)
        
        self.total_checks += 1
    
    def generate_report(self):
        """生成验证报告"""
        print("\n" + "="*60)
        print("验证报告")
        print("="*60)
        
        print(f"总检查项: {self.total_checks}")
        print(f"通过: {self.success_count}")
        print(f"失败: {len(self.errors)}")
        print(f"警告: {len(self.warnings)}")
        
        if self.errors:
            print(f"\n❌ 发现 {len(self.errors)} 个错误:")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
        
        if self.warnings:
            print(f"\n⚠️  发现 {len(self.warnings)} 个警告:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
        
        success_rate = (self.success_count / self.total_checks * 100) if self.total_checks > 0 else 0
        print(f"\n成功率: {success_rate:.1f}%")
        
        if len(self.errors) == 0:
            print("\n🎉 工程验证通过！")
            return True
        else:
            print("\n❌ 工程验证失败，请修复上述错误。")
            return False
    
    def run_verification(self):
        """运行完整验证"""
        print("N100电源控制系统工程验证")
        print("="*60)
        print(f"项目根目录: {self.project_root}")
        
        # 执行各项验证
        self.verify_project_structure()
        self.verify_source_code()
        self.verify_services()
        self.verify_scripts()
        self.verify_examples()
        self.verify_tests()
        self.verify_documentation()
        self.verify_dependencies()
        
        # 生成报告
        return self.generate_report()


def main():
    """主函数"""
    verifier = ProjectVerifier()
    
    try:
        success = verifier.run_verification()
        return 0 if success else 1
    
    except KeyboardInterrupt:
        print("\n⚠️  验证被用户中断")
        return 1
    
    except Exception as e:
        print(f"\n❌ 验证过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit(main())
