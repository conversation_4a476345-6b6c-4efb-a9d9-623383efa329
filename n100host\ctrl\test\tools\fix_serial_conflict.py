#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
串口冲突检测和修复脚本
检测并解决ttyS4串口的访问冲突问题
"""

import os
import sys
import time
import subprocess

def check_serial_processes():
    """检查占用串口的进程"""
    print("=== 检查串口占用情况 ===")
    
    try:
        # 使用lsof检查串口占用
        result = subprocess.run(['lsof', '/dev/ttyS4'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("⚠️ 串口被以下进程占用:")
            print(result.stdout)
            
            # 解析进程信息
            lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            pids = []
            for line in lines:
                parts = line.split()
                if len(parts) >= 2:
                    pid = parts[1]
                    pids.append(pid)
            
            return pids
        else:
            print("✅ 串口未被其他进程占用")
            return []
            
    except Exception as e:
        print(f"无法检查进程占用: {e}")
        return []

def check_services():
    """检查相关系统服务状态"""
    print("\n=== 检查系统服务状态 ===")
    
    services = ['n100-serial-manager', 'n100-shutdown']
    service_status = {}
    
    for service in services:
        try:
            result = subprocess.run(['systemctl', 'is-active', service], 
                                  capture_output=True, text=True)
            status = result.stdout.strip()
            service_status[service] = status
            
            if status == 'active':
                print(f"✅ {service}: 运行中")
            else:
                print(f"❌ {service}: {status}")
                
        except Exception as e:
            print(f"❌ {service}: 检查失败 - {e}")
            service_status[service] = 'unknown'
    
    return service_status

def stop_conflicting_processes(pids):
    """停止冲突的进程"""
    print(f"\n=== 停止冲突进程 ===")
    
    for pid in pids:
        try:
            print(f"停止进程 {pid}...")
            subprocess.run(['kill', '-TERM', pid], check=True)
            time.sleep(1)
            
            # 检查进程是否还在运行
            result = subprocess.run(['kill', '-0', pid], 
                                  capture_output=True)
            if result.returncode == 0:
                print(f"强制停止进程 {pid}...")
                subprocess.run(['kill', '-KILL', pid], check=True)
            
            print(f"✅ 进程 {pid} 已停止")
            
        except subprocess.CalledProcessError:
            print(f"✅ 进程 {pid} 已不存在")
        except Exception as e:
            print(f"❌ 停止进程 {pid} 失败: {e}")

def restart_services():
    """重启相关服务"""
    print(f"\n=== 重启系统服务 ===")
    
    services = ['n100-serial-manager', 'n100-shutdown']
    
    # 先停止所有服务
    for service in services:
        try:
            print(f"停止 {service}...")
            subprocess.run(['systemctl', 'stop', service], check=True)
        except Exception as e:
            print(f"停止 {service} 失败: {e}")
    
    time.sleep(2)
    
    # 重新启动服务
    for service in services:
        try:
            print(f"启动 {service}...")
            subprocess.run(['systemctl', 'start', service], check=True)
            time.sleep(1)
            
            # 检查服务状态
            result = subprocess.run(['systemctl', 'is-active', service], 
                                  capture_output=True, text=True)
            status = result.stdout.strip()
            
            if status == 'active':
                print(f"✅ {service} 启动成功")
            else:
                print(f"❌ {service} 启动失败: {status}")
                
        except Exception as e:
            print(f"❌ 启动 {service} 失败: {e}")

def test_serial_access():
    """测试串口访问"""
    print(f"\n=== 测试串口访问 ===")
    
    try:
        import serial
        
        # 尝试打开串口
        ser = serial.Serial('/dev/ttyS4', 115200, timeout=1)
        print("✅ 串口打开成功")
        
        # 发送测试数据
        test_data = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
        ser.write(test_data)
        ser.flush()
        print(f"✅ 测试数据发送成功: {test_data.hex(' ').upper()}")
        
        # 尝试读取
        time.sleep(0.5)
        if ser.in_waiting > 0:
            response = ser.read(ser.in_waiting)
            print(f"✅ 收到响应: {response.hex(' ').upper()}")
        else:
            print("⚠️ 未收到响应（可能是正常的）")
        
        ser.close()
        print("✅ 串口测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 串口测试失败: {e}")
        return False

def main():
    """主函数"""
    print("N100串口冲突检测和修复")
    print("=" * 40)
    
    # 检查是否为root用户
    if os.geteuid() != 0:
        print("❌ 需要root权限运行此脚本")
        print("请使用: sudo python3 fix_serial_conflict.py")
        return 1
    
    # 1. 检查串口占用
    pids = check_serial_processes()
    
    # 2. 检查服务状态
    service_status = check_services()
    
    # 3. 如果有冲突，提供解决方案
    if pids:
        print(f"\n⚠️ 检测到串口冲突，有 {len(pids)} 个进程占用串口")
        
        choice = input("是否停止冲突进程？(y/N): ").strip().lower()
        if choice == 'y':
            stop_conflicting_processes(pids)
        else:
            print("跳过停止冲突进程")
    
    # 4. 重启服务
    if any(status != 'active' for status in service_status.values()):
        choice = input("是否重启系统服务？(y/N): ").strip().lower()
        if choice == 'y':
            restart_services()
        else:
            print("跳过重启服务")
    
    # 5. 测试串口访问
    print(f"\n测试串口访问...")
    serial_ok = test_serial_access()
    
    # 6. 显示建议
    print(f"\n=== 修复建议 ===")
    
    if serial_ok:
        print("✅ 串口访问正常")
        print("\n建议的测试命令:")
        print("1. 使用串口管理器模式:")
        print("   sudo python3 src/power_ctrl_cli.py test")
        print("\n2. 使用直接模式:")
        print("   sudo python3 src/power_ctrl_cli.py --no-manager test")
    else:
        print("❌ 串口访问仍有问题")
        print("\n建议:")
        print("1. 检查硬件连接")
        print("2. 确认串口权限: sudo chmod 666 /dev/ttyS4")
        print("3. 重启系统")
        print("4. 使用直接模式: sudo python3 src/power_ctrl_cli.py --no-manager test")
    
    return 0 if serial_ok else 1

if __name__ == "__main__":
    sys.exit(main())
