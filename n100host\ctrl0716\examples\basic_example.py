#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制器基础使用示例
演示如何使用N100PowerController类进行基本的电源控制操作
"""

import time
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))

from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod


def ack_callback(command):
    """ACK接收回调函数"""
    print(f"[回调] 收到ACK应答，命令: 0x{command:02X}")


def error_callback(error_msg):
    """错误回调函数"""
    print(f"[回调] 发生错误: {error_msg}")


def basic_usage_example():
    """基础使用示例"""
    print("=== N100电源控制器基础使用示例 ===")
    
    # 创建控制器实例
    controller = N100PowerController(
        port='/dev/ttyS4',      # 串口设备
        baudrate=115200,        # 波特率
        timeout=1.0,           # 超时时间
        max_retries=10         # 最大重试次数
    )
    
    # 设置回调函数
    controller.set_ack_callback(ack_callback)
    controller.set_error_callback(error_callback)
    
    # 连接串口
    print("\n1. 连接串口...")
    if not controller.connect():
        print("❌ 错误: 无法连接到串口")
        return False
    
    print("✅ 串口连接成功")
    
    try:
        # 设置LED为正常模式
        print("\n2. 设置LED为正常模式...")
        if controller.set_led_normal():
            print("✅ LED正常模式设置成功")
        else:
            print("❌ LED正常模式设置失败")
        
        time.sleep(2)
        
        # 设置LED为呼吸模式
        print("\n3. 设置LED为呼吸模式...")
        if controller.set_led_breath():
            print("✅ LED呼吸模式设置成功")
        else:
            print("❌ LED呼吸模式设置失败")
        
        time.sleep(2)
        
        # 设置呼吸周期为3秒
        print("\n4. 设置呼吸周期为3秒...")
        if controller.set_breath_3s():
            print("✅ 呼吸周期设置成功")
        else:
            print("❌ 呼吸周期设置失败")
        
        time.sleep(2)
        
        # 发送关机成功消息
        print("\n5. 发送关机成功消息...")
        if controller.send_shutdown_success():
            print("✅ 关机成功消息发送成功")
        else:
            print("❌ 关机成功消息发送失败")
        
        print("\n✅ 基础示例执行完成")
        return True
        
    except Exception as e:
        print(f"❌ 执行过程中发生异常: {e}")
        return False
        
    finally:
        # 断开连接
        print("\n6. 断开连接...")
        controller.disconnect()
        print("✅ 连接已断开")


def proxy_usage_example():
    """代理模式使用示例"""
    print("\n=== 代理模式使用示例 ===")
    
    # 创建控制器实例（使用代理模式）
    controller = N100PowerController(
        port='/dev/ttyS4',
        use_proxy=True  # 启用代理模式
    )
    
    # 设置回调函数
    controller.set_ack_callback(ack_callback)
    controller.set_error_callback(error_callback)
    
    # 连接代理服务
    print("\n1. 连接代理服务...")
    if not controller.connect():
        print("❌ 错误: 无法连接到代理服务")
        print("请确保串口代理服务正在运行:")
        print("  sudo systemctl start n100-serial-proxy")
        return False
    
    print("✅ 代理服务连接成功")
    
    try:
        # 测试LED控制
        print("\n2. 测试LED控制...")
        for mode_name, mode_func in [("正常", controller.set_led_normal), ("呼吸", controller.set_led_breath)]:
            print(f"  设置LED为{mode_name}模式...")
            if mode_func():
                print(f"  ✅ LED{mode_name}模式设置成功")
            else:
                print(f"  ❌ LED{mode_name}模式设置失败")
            time.sleep(1)
        
        # 测试呼吸周期
        print("\n3. 测试呼吸周期...")
        for period, func in [(1, controller.set_breath_1s), (3, controller.set_breath_3s), (5, controller.set_breath_5s)]:
            print(f"  设置呼吸周期为{period}秒...")
            if func():
                print(f"  ✅ {period}秒呼吸周期设置成功")
            else:
                print(f"  ❌ {period}秒呼吸周期设置失败")
            time.sleep(1)
        
        print("\n✅ 代理模式示例执行完成")
        return True
        
    except Exception as e:
        print(f"❌ 执行过程中发生异常: {e}")
        return False
        
    finally:
        # 断开连接
        print("\n4. 断开连接...")
        controller.disconnect()
        print("✅ 连接已断开")


def status_check_example():
    """状态检查示例"""
    print("\n=== 状态检查示例 ===")
    
    controller = N100PowerController()
    
    # 检查串口状态（不连接）
    print("\n1. 检查串口信息...")
    info = controller.get_port_info()
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    # 连接并检查状态
    print("\n2. 连接并检查状态...")
    if controller.connect():
        print("✅ 串口连接成功")
        print(f"  连接状态: {controller.is_port_connected()}")
        controller.disconnect()
    else:
        print("❌ 串口连接失败")


def main():
    """主函数"""
    print("N100电源控制器基础使用示例")
    print("=" * 50)
    
    try:
        # 运行基础示例
        if not basic_usage_example():
            print("\n❌ 基础示例执行失败")
            return 1
        
        time.sleep(2)
        
        # 运行代理模式示例
        if not proxy_usage_example():
            print("\n⚠️  代理模式示例执行失败（可能代理服务未运行）")
        
        time.sleep(2)
        
        # 运行状态检查示例
        status_check_example()
        
        print("\n🎉 所有示例执行完成!")
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 执行过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
