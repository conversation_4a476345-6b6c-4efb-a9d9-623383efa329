# N100电源控制系统配置文件示例
# 复制此文件为 config.yml 并根据需要修改配置

# 串口配置
serial:
  # 串口设备路径
  port: "/dev/ttyS4"
  
  # 波特率
  baudrate: 115200
  
  # 数据位
  bytesize: 8
  
  # 停止位
  stopbits: 1
  
  # 校验位 (none, even, odd, mark, space)
  parity: "none"
  
  # 读取超时时间（秒）
  timeout: 1.0
  
  # 写入超时时间（秒）
  write_timeout: 1.0

# 通信配置
communication:
  # 最大重试次数
  max_retries: 10
  
  # 重试间隔（秒）
  retry_interval: 0.1
  
  # ACK等待超时（秒）
  ack_timeout: 1.0
  
  # 命令间延迟（秒）
  command_delay: 0.05

# 代理配置
proxy:
  # 是否启用代理模式
  enabled: false
  
  # Unix Socket路径
  socket_path: "/tmp/n100_serial_proxy.sock"
  
  # Socket权限
  socket_mode: 0o666
  
  # 客户端超时（秒）
  client_timeout: 5.0
  
  # 最大客户端连接数
  max_clients: 10

# 关机配置
shutdown:
  # 关机守护进程类型 (direct, proxy)
  daemon_type: "direct"
  
  # 关机命令
  shutdown_command: ["sudo", "shutdown", "-h", "now"]
  
  # 关机前延迟（秒）
  shutdown_delay: 0.5
  
  # 关机成功通知延迟（秒）
  success_notify_delay: 2.0

# 日志配置
logging:
  # 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  level: "INFO"
  
  # 日志格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 日志文件路径
  file: "/var/log/n100_power_ctrl.log"
  
  # 日志文件最大大小（MB）
  max_size: 10
  
  # 保留的日志文件数量
  backup_count: 5
  
  # 是否输出到控制台
  console: true

# 服务配置
service:
  # 串口代理服务配置
  serial_proxy:
    # 服务名称
    name: "n100-serial-proxy"
    
    # 服务描述
    description: "N100 Serial Proxy Daemon"
    
    # 启动用户
    user: "root"
    
    # 启动组
    group: "root"
    
    # 重启策略
    restart: "always"
    
    # 重启延迟（秒）
    restart_sec: 5
  
  # 关机服务配置
  shutdown:
    # 服务名称
    name: "n100-shutdown"
    
    # 服务描述
    description: "N100 Shutdown Daemon"
    
    # 启动用户
    user: "root"
    
    # 启动组
    group: "root"
    
    # 重启策略
    restart: "always"
    
    # 重启延迟（秒）
    restart_sec: 5

# 监控配置
monitoring:
  # 是否启用监控
  enabled: false
  
  # 监控间隔（秒）
  interval: 60
  
  # 性能指标收集
  metrics:
    # CPU使用率
    cpu: true
    
    # 内存使用率
    memory: true
    
    # 串口状态
    serial_status: true
    
    # 命令统计
    command_stats: true
  
  # 告警配置
  alerts:
    # CPU使用率告警阈值（%）
    cpu_threshold: 80
    
    # 内存使用率告警阈值（%）
    memory_threshold: 80
    
    # 连续失败命令告警阈值
    failed_commands_threshold: 5

# 安全配置
security:
  # 是否启用访问控制
  access_control: false
  
  # 允许的客户端IP列表
  allowed_clients:
    - "127.0.0.1"
    - "::1"
  
  # API密钥（如果启用）
  api_key: ""
  
  # 是否启用SSL/TLS
  ssl_enabled: false
  
  # SSL证书路径
  ssl_cert: ""
  
  # SSL私钥路径
  ssl_key: ""

# 开发配置
development:
  # 是否启用调试模式
  debug: false
  
  # 是否启用详细日志
  verbose: false
  
  # 测试模式
  test_mode: false
  
  # 模拟串口
  mock_serial: false
  
  # 模拟电源板响应
  mock_power_board: false

# 高级配置
advanced:
  # 串口缓冲区大小
  serial_buffer_size: 4096
  
  # 接收缓冲区大小
  rx_buffer_size: 1024
  
  # 发送队列大小
  tx_queue_size: 100
  
  # 线程池大小
  thread_pool_size: 4
  
  # 是否启用流控制
  flow_control: false
  
  # 是否启用RTS/CTS
  rtscts: false
  
  # 是否启用DSR/DTR
  dsrdtr: false

# 协议配置
protocol:
  # 帧头
  frame_header: 0xAA
  
  # 帧尾
  frame_tail: 0x55
  
  # 最大帧长度
  max_frame_length: 20
  
  # 最大数据长度
  max_data_length: 13
  
  # 校验和算法 (complement, crc8, crc16)
  checksum_algorithm: "complement"
  
  # 是否启用帧验证
  frame_validation: true
  
  # 是否自动发送ACK
  auto_ack: true

# 备份配置
backup:
  # 是否启用自动备份
  enabled: false
  
  # 备份目录
  directory: "/backup/n100"
  
  # 备份间隔（小时）
  interval: 24
  
  # 保留备份数量
  retention: 7
  
  # 备份内容
  include:
    - "config"
    - "logs"
    - "services"
