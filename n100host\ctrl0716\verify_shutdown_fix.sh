#!/bin/bash

# N100关机流程修复验证脚本
# 验证代理架构下的关机流程是否正常工作

set -e

echo "=== N100关机流程修复验证 ==="
echo "验证时间: $(date)"
echo ""

# 检查服务状态
echo "1. 检查服务状态..."
services=("n100-serial-proxy" "n100-shutdown-proxy")
all_running=true

for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service"; then
        echo "  ✅ $service: 运行中"
    else
        echo "  ❌ $service: 未运行"
        all_running=false
    fi
done

if [ "$all_running" = false ]; then
    echo ""
    echo "⚠️  部分服务未运行，请先启动服务："
    echo "  sudo systemctl start n100-serial-proxy"
    echo "  sudo systemctl start n100-shutdown-proxy"
    echo ""
    read -p "是否继续验证？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo ""

# 检查串口设备
echo "2. 检查串口设备..."
if [ -e /dev/ttyS4 ]; then
    echo "  ✅ 串口设备存在: /dev/ttyS4"
    ls -l /dev/ttyS4
else
    echo "  ❌ 串口设备不存在: /dev/ttyS4"
    echo "  请检查硬件连接"
fi

echo ""

# 检查代理Socket
echo "3. 检查代理Socket..."
if [ -e /tmp/n100_serial_proxy.sock ]; then
    echo "  ✅ 代理Socket存在: /tmp/n100_serial_proxy.sock"
    ls -l /tmp/n100_serial_proxy.sock
else
    echo "  ❌ 代理Socket不存在"
    echo "  请检查串口代理服务状态"
fi

echo ""

# 检查进程
echo "4. 检查进程..."
if pgrep -f "serial_proxy_daemon" > /dev/null; then
    echo "  ✅ 串口代理守护进程运行中"
    echo "    PID: $(pgrep -f 'serial_proxy_daemon')"
else
    echo "  ❌ 串口代理守护进程未运行"
fi

if pgrep -f "n100_shutdown_daemon_proxy" > /dev/null; then
    echo "  ✅ 关机守护进程运行中"
    echo "    PID: $(pgrep -f 'n100_shutdown_daemon_proxy')"
else
    echo "  ❌ 关机守护进程未运行"
fi

echo ""

# 检查Python环境
echo "5. 检查Python环境..."
if command -v python3 &> /dev/null; then
    python_version=$(python3 --version)
    echo "  ✅ Python: $python_version"
else
    echo "  ❌ Python3 未安装"
fi

if python3 -c "import serial" 2>/dev/null; then
    echo "  ✅ pyserial 模块可用"
else
    echo "  ❌ pyserial 模块未安装"
    echo "  安装命令: pip3 install pyserial"
fi

echo ""

# 运行关机流程测试
echo "6. 运行关机流程测试..."
if [ -f "test_shutdown_flow.py" ]; then
    echo "  执行测试脚本..."
    if python3 test_shutdown_flow.py; then
        echo "  ✅ 关机流程测试通过"
    else
        echo "  ❌ 关机流程测试失败"
    fi
else
    echo "  ⚠️  测试脚本不存在，跳过自动测试"
    echo "  手动测试命令:"
    echo "    python3 test_shutdown_flow.py"
fi

echo ""

# 检查最新日志
echo "7. 检查最新日志..."
echo "  串口代理服务日志 (最近5条):"
journalctl -u n100-serial-proxy -n 5 --no-pager | sed 's/^/    /'

echo ""
echo "  关机守护进程日志 (最近5条):"
journalctl -u n100-shutdown-proxy -n 5 --no-pager | sed 's/^/    /'

echo ""

# 提供手动测试指令
echo "8. 手动测试指令..."
echo "  如需手动测试关机流程，可以使用以下命令:"
echo ""
echo "  # 模拟发送关机请求 (谨慎使用，会触发实际关机)"
echo "  echo -ne '\\xAA\\x01\\x13\\xED\\x55' | sudo tee /dev/ttyS4"
echo ""
echo "  # 查看实时日志"
echo "  sudo journalctl -u n100-serial-proxy -u n100-shutdown-proxy -f"
echo ""
echo "  # 测试LED命令 (安全测试)"
echo "  cd $(pwd)/src && python3 power_ctrl_cli.py --use-proxy led normal"

echo ""

# 总结
echo "=== 验证总结 ==="
echo "修复内容:"
echo "  ✅ 串口代理对所有命令自动发送ACK应答"
echo "  ✅ 关机守护进程不再重复发送ACK"
echo "  ✅ 优化关机时序，先发送关机成功消息"
echo "  ✅ 添加消息过滤，关机守护进程只接收关机请求"
echo ""
echo "期望的关机流程:"
echo "  1. 外部设备 → 串口发送关机请求 (AA 01 13 ED 55)"
echo "  2. 串口代理 → 立即发送ACK应答 (AA 01 80 80 55)"
echo "  3. 串口代理 → 转发关机请求给关机守护进程"
echo "  4. 关机守护进程 → 执行关机脚本"
echo "  5. 关机守护进程 → 发送关机成功消息 (AA 01 03 FD 55)"
echo "  6. 系统 → 执行关机"

echo ""
echo "🎉 关机流程修复验证完成！"
echo ""
echo "注意事项:"
echo "  - 实际关机测试会导致系统关机，请谨慎操作"
echo "  - 建议在测试环境中验证完整流程"
echo "  - 生产环境部署前请充分测试"
